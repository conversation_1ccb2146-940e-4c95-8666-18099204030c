pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
        maven { setUrl("https://jitpack.io") }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { setUrl("https://jitpack.io") }
        //融云 maven 仓库地址
        maven { setUrl("https://maven.rongcloud.cn/repository/maven-releases/") }
        maven { setUrl("https://maven.aliyun.com/repository/public") }
    }
}

rootProject.name = "Facee"
include(":app")
include(":netlibrary")
