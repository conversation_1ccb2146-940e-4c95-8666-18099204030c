<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/CL"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/call_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        tools:text="No one answered"
        android:layout_marginEnd="@dimen/dp_8"
        android:textSize="@dimen/sp_14" />

    <ImageView
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:src="@mipmap/ic_message_video_call" />

    <TextView
        android:id="@+id/call_time_receive"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        tools:text="No one answered"
        android:layout_marginStart="@dimen/dp_8"
        android:visibility="gone"
        android:textSize="@dimen/sp_14" />


</LinearLayout>