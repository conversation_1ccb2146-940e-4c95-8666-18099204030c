<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/CL"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="6dp">

    <com.mobile.app.facee.i18n.I18nTextView
        android:id="@+id/label_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/title_gift_ask"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/gift"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:layout_marginTop="@dimen/dp_4"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/label_tip" />

    <TextView
        android:id="@+id/giftName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_6"
        android:ellipsize="end"
        android:maxWidth="@dimen/dp_120"
        android:maxLines="1"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="@id/gift"
        app:layout_constraintStart_toEndOf="@id/gift"
        app:layout_constraintTop_toTopOf="@id/gift"  />

    <com.mobile.app.facee.i18n.I18nTextView
        android:id="@+id/fast_give"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:text="@string/rc_send"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_11"
        android:paddingHorizontal="@dimen/dp_10"
        android:paddingVertical="@dimen/dp_5"
        android:background="@drawable/shape_message_gift_send"
        app:layout_constraintStart_toEndOf="@id/label_tip"
        app:layout_constraintTop_toTopOf="@id/label_tip" />

</androidx.constraintlayout.widget.ConstraintLayout>