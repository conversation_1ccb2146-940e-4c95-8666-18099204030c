<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_content"
    android:layout_width="wrap_content"
    android:layout_height="100dp">

    <ImageView
        android:id="@+id/rc_image"
        android:layout_width="@dimen/dp_100"
        android:layout_height="@dimen/dp_100"
        android:scaleType="centerCrop" />

    <View
        android:id="@+id/main_bg"
        android:layout_width="@dimen/dp_100"
        android:layout_height="@dimen/dp_100"
        android:background="@color/picture_color_80" />

    <LinearLayout
        android:id="@+id/rl_progress"
        android:layout_width="@dimen/dp_100"
        android:layout_height="@dimen/dp_100"
        android:gravity="center"
        android:orientation="vertical">

        <ProgressBar
            android:id="@+id/rc_progress"
            style="?android:attr/progressBarStyle"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:indeterminateDrawable="@drawable/rc_progress_sending_style" />

        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/rc_margin_size_2"
            android:text="0%"
            android:textColor="@color/rc_white_color"
            android:textSize="@dimen/rc_font_describe_size" />
    </LinearLayout>

    <ImageView
        android:id="@+id/rc_image_block"
        android:layout_width="@dimen/dp_18"
        android:layout_height="@dimen/dp_18"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="@dimen/dp_6"
        android:layout_toEndOf="@+id/rc_image"
        android:src="@mipmap/icon_message_block"
        android:visibility="gone" />

</RelativeLayout>



