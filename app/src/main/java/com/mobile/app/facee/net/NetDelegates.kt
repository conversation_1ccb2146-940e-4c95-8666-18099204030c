package com.mobile.app.facee.net

import kotlin.properties.ReadOnlyProperty
import kotlin.reflect.KClass
import kotlin.reflect.KProperty

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/17 2:53 下午
 * @description： 自定义委托 封装retrofit getService
 */
class NetDelegates<T : Any> (private val url : String = NetManager.baseURL): ReadOnlyProperty<Any?,T> {

    override fun getValue(thisRef: Any?, property: KProperty<*>): T {
        val cf = property.returnType.classifier as KClass<T>
        return NetManager.getService(cf.java, url)
    }

}