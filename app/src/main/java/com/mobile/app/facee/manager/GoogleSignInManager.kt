package com.mobile.app.facee.manager

import android.content.Context
import android.content.Intent
import androidx.activity.result.ActivityResultLauncher
import androidx.fragment.app.FragmentActivity
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.tasks.Task
import com.mobile.app.facee.data.network.ApiResult
import com.mobile.app.facee.trace.LogX

/**
 * Google登录管理类
 */
class GoogleSignInManager(
    private val context: Context,
    private val signInLauncher: ActivityResultLauncher<Intent>,
    private val onSignInResult: (ApiResult<GoogleSignInAccount>) -> Unit
) {
    private var googleSignInClient: GoogleSignInClient? = null

    /**
     * 初始化Google登录
     */
    fun initialize(activity: FragmentActivity) {
        // 配置Google登录选项，使用 google-services.json 中的配置
        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN).requestEmail()
            .requestIdToken("************-khahnbeh6tm8da9rvinj6qr1hk2a2lrd.apps.googleusercontent.com") // 使用 google-services.json 中的 web client_id
            .build()

        googleSignInClient = GoogleSignIn.getClient(activity, gso)
    }

    /**
     * 开始Google登录
     */
    fun signIn() {
        val signInIntent = googleSignInClient?.signInIntent
        if (signInIntent != null) {
            signInLauncher.launch(signInIntent)
        } else {
            onSignInResult(ApiResult.Error(exception = Exception("Google login not initialized")))
        }
    }

    /**
     * 处理登录结果
     */
    fun handleSignInResult(data: Intent?) {
        try {
            val task: Task<GoogleSignInAccount> = GoogleSignIn.getSignedInAccountFromIntent(data)
            val account = task.getResult(ApiException::class.java)

            if (account != null) {
                LogX.d("Google登录成功: ${account.email}")
                onSignInResult(ApiResult.Success(account))
            } else {
                LogX.e("Google登录失败: account为null")
                onSignInResult(ApiResult.Error(exception = Exception("Login failed")))
            }
        } catch (e: ApiException) {
            LogX.e("Google登录异常: ${e.statusCode}", e)
            when (e.statusCode) {
                12501 -> {
                    // 用户取消登录，这不算错误，返回特殊的取消状态
                    LogX.d("用户取消Google登录")
                    onSignInResult(ApiResult.Error(exception = Exception("User cancel login")))
                }

                else -> onSignInResult(ApiResult.Error(exception = Exception("Login failed: ${e.message}")))
            }
        } catch (e: Exception) {
            LogX.e("Google登录未知异常", e)
            onSignInResult(ApiResult.Error(exception = e))
        }
    }

    /**
     * 登出
     */
    fun signOut() {
        googleSignInClient?.signOut()?.addOnCompleteListener {
            LogX.d("Google登出完成")
        }
    }

    /**
     * 撤销访问权限
     */
    fun revokeAccess() {
        googleSignInClient?.revokeAccess()?.addOnCompleteListener {
            LogX.d("Google撤销访问权限完成")
        }
    }

    /**
     * 检查是否已经登录
     */
    fun getLastSignedInAccount(): GoogleSignInAccount? {
        return GoogleSignIn.getLastSignedInAccount(context)
    }
}
