package com.mobile.app.facee.trace


import com.mobile.app.facee.utils.AppContextHolder
import com.orhanobut.logger.Logger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File


object TraceXUploadQueue {
    private val queueDir by lazy {
        File(AppContextHolder.getApplication().filesDir, "logs").apply { mkdirs() }
    }

    fun enqueue(file: File, onComplete: ((Boolean) -> Unit)? = null) {
        try {
            Logger.d("UploadQueueManager: 尝试入队文件: ${file.absolutePath}")

            if (!file.exists()) {
                Logger.w("UploadQueueManager: 文件不存在: ${file.absolutePath}")
                onComplete?.invoke(false)
                return
            }

            if (file.length() == 0L) {
                Logger.w("UploadQueueManager: 文件为空: ${file.absolutePath}")
                file.delete() // 删除空文件
                onComplete?.invoke(false)
                return
            }

            Logger.d("UploadQueueManager: 文件存在，大小: ${file.length()} bytes")

            val dest = File(queueDir, file.name)

            // 确保目标目录存在
            if (!queueDir.exists()) {
                queueDir.mkdirs()
            }

            // 直接上传原文件，不进行复制和删除
            Logger.d("UploadQueueManager: 直接上传文件: ${file.name}, 大小: ${file.length()} bytes")
            tryUpload(file, onComplete)
        } catch (e: Exception) {
            Logger.e("UploadQueueManager: 日志文件入队失败: ${file.name}, error: ${e.message}")
            e.printStackTrace()
            onComplete?.invoke(false)
        }
    }

    fun tryUpload(file: File, onComplete: ((Boolean) -> Unit)? = null) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Logger.d("UploadQueueManager: 准备上传文件: ${file.absolutePath}")

                if (!file.exists()) {
                    Logger.w("UploadQueueManager: 尝试上传不存在的文件: ${file.absolutePath}")
                    onComplete?.invoke(false)
                    return@launch
                }

                Logger.d("UploadQueueManager: 文件存在，开始上传: ${file.name}, 大小: ${file.length()} bytes")

                TraceXUploader.upload(file) {

                    // 上传成功，不在这里删除文件，由 LogX 负责删除
                    Logger.d("UploadQueueManager: 日志文件上传成功: ${file.name}")

                    // 调用完成回调，通知上传成功
                    onComplete?.invoke(it)
                }
            } catch (e: Exception) {
                Logger.e("UploadQueueManager: 日志文件上传失败: ${file.name}, error: ${e.message}")
                e.printStackTrace()
                // 上传失败，通知调用方
                onComplete?.invoke(false)
            }
        }
    }

    fun retryAllPending() {
        try {
            val pendingFiles = queueDir.listFiles()
            if (pendingFiles.isNullOrEmpty()) {
                Logger.d("UploadQueueManager: 没有待上传的日志文件")
                return
            }

            Logger.d("UploadQueueManager: 发现 ${pendingFiles.size} 个待上传的日志文件")
            pendingFiles.forEach { file ->
                if (file.isFile && file.name.endsWith(".txt")) {
                    Logger.d("UploadQueueManager: 重试上传日志文件: ${file.name}")
                    tryUpload(file)
                }
            }
        } catch (e: Exception) {
            Logger.e("UploadQueueManager: 重试上传日志文件失败: ${e.message}")
            e.printStackTrace()
        }
    }
}
