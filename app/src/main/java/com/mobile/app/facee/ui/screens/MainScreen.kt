package com.mobile.app.facee.ui.screens

import com.slack.circuit.runtime.CircuitUiEvent
import com.slack.circuit.runtime.CircuitUiState
import com.slack.circuit.runtime.screen.Screen
import kotlinx.parcelize.Parcelize

/**
 * 主屏幕 - 包含底部导航的容器屏幕
 */
@Parcelize
data object MainScreen : Screen {

    /**
     * 主屏幕状态
     */
    data class State(
        val selectedTab: TabItem,
        val eventSink: (Event) -> Unit
    ) : CircuitUiState

    /**
     * 主屏幕事件
     */
    sealed interface Event : CircuitUiEvent {
        data class TabSelected(val tab: TabItem) : Event
    }

    /**
     * 底部导航标签项
     */
    enum class TabItem(
        val title: String,
        val screen: AppScreen
    ) {
        HOME("首页", AppScreen.HomeScreen),
        MATCH("匹配", AppScreen.MatchScreen),
        MESSAGE("消息", AppScreen.MessageScreen),
        PROFILE("我的", AppScreen.MineScreen)
    }
}
