package com.mobile.app.facee.data.service

import com.facee.netlibrary.coroutines.Await
import com.mobile.app.facee.data.model.CallHistoryBean
import com.mobile.app.facee.data.model.PageSource
import com.mobile.app.facee.data.model.TabBean
import com.mobile.app.facee.data.model.UserBean
import retrofit2.http.GET
import retrofit2.http.Query

interface UserApiService {

    @GET("api/v1/user/tab")
    suspend fun getTabList(): Await<PageSource<TabBean>>

    @GET("/api/v1/user/homepage")
    suspend fun getHomepage(
        @Query("home_page_type") type: Int,
        @Query("cursor") cursor: String,
        @Query("size") size: Int
    ): Await<PageSource<UserBean>>

    @GET("/api/v1/user/call_record/list")
    suspend fun getVideoHistory(
        @Query("cursor") cursor: String,
        @Query("size") pageSize: Int
    ): Await<PageSource<CallHistoryBean>?>
}