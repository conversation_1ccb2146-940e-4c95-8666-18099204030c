package com.mobile.app.facee.data.network

import android.annotation.SuppressLint
import com.google.gson.GsonBuilder
import kotlinx.serialization.json.Json
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.converter.kotlinx.serialization.asConverterFactory
import java.security.SecureRandom
import java.security.cert.CertificateException
import java.security.cert.X509Certificate
import java.util.concurrent.TimeUnit
import javax.net.ssl.HostnameVerifier
import javax.net.ssl.SSLContext
import javax.net.ssl.SSLSocketFactory
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager

/**
 * 网络客户端单例
 * 负责配置和提供 Retrofit 实例
 */
object NetworkClient {

    private val networkConfig = NetworkConfig
    private val authManager = AuthManager
    private val requestParamsBuilder = RequestParamsBuilder

    // JSON 配置
    private val json = Json {
        ignoreUnknownKeys = true
        coerceInputValues = true
        encodeDefaults = true
    }

    // OkHttp 客户端
    private val okHttpClient: OkHttpClient by lazy {
        OkHttpClient.Builder().connectTimeout(networkConfig.getConnectTimeout(), TimeUnit.SECONDS)
            .readTimeout(networkConfig.getReadTimeout(), TimeUnit.SECONDS)
            .writeTimeout(networkConfig.getWriteTimeout(), TimeUnit.SECONDS)
            .addInterceptor(createRequestParamsInterceptor())
            .addInterceptor(createLoggingInterceptor()).addInterceptor(createAuthInterceptor())
            .addInterceptor(DetailedLogInterceptor())
            .sslSocketFactory(SSLSocketClient.sSLSocketFactory, SSLSocketClient.trustManager)
            .hostnameVerifier(SSLSocketClient.hostnameVerifier).build()
    }

    // Retrofit 实例
    private val retrofit: Retrofit by lazy {
        Retrofit.Builder().baseUrl(networkConfig.getBaseUrl()).client(okHttpClient)
            .addConverterFactory(
                GsonConverterFactory.create(
                    GsonBuilder().setLenient().registerTypeAdapterFactory(ApiResultAdapterFactory())
                        .create()
                )
            ).addConverterFactory(json.asConverterFactory("application/json".toMediaType())).build()
    }

    /**
     * 创建API服务
     */
    fun <T> createService(serviceClass: Class<T>): T {
        return retrofit.create(serviceClass)
    }

    /**
     * 创建日志拦截器
     */
    private fun createLoggingInterceptor(): HttpLoggingInterceptor {
        return HttpLoggingInterceptor().apply {
            level = if (networkConfig.isLoggingEnabled()) {
                HttpLoggingInterceptor.Level.BODY
            } else {
                HttpLoggingInterceptor.Level.NONE
            }
        }
    }

    /**
     * 创建鉴权拦截器
     */
    private fun createAuthInterceptor() = Interceptor { chain ->
        val originalRequest = chain.request()
        val authHeaders = authManager.createAuthHeaders()

        val newRequestBuilder = originalRequest.newBuilder()
        authHeaders.forEach { (key, value) ->
            newRequestBuilder.addHeader(key, value)
        }

        val newRequest = newRequestBuilder.build()
        chain.proceed(newRequest)
    }

    /**
     * 创建通用参数拦截器
     */
    private fun createRequestParamsInterceptor() = Interceptor { chain ->
        val originalRequest = chain.request()
        val originalUrl = originalRequest.url

        // 获取通用参数
        val params = requestParamsBuilder.create()

        // 构建新的URL，添加通用查询参数
        val newUrlBuilder = originalUrl.newBuilder()
        params.forEach { (key, value) ->
            newUrlBuilder.addQueryParameter(key, value.toString())
        }

        val newRequest = originalRequest.newBuilder().url(newUrlBuilder.build()).build()

        chain.proceed(newRequest)
    }

    /**
     * 获取鉴权管理器
     */
    fun getAuthManager(): AuthManager {
        return authManager
    }

    /**
     * 获取通用参数管理器
     */
    fun getApiParamsBuilder(): RequestParamsBuilder {
        return requestParamsBuilder
    }

    private object SSLSocketClient {
        //获取这个SSLSocketFactory
        val sSLSocketFactory: SSLSocketFactory
            get() = try {
                val sslContext = SSLContext.getInstance("SSL")
                sslContext.init(
                    null, trustManagers, SecureRandom()
                )
                sslContext.socketFactory
            } catch (e: Exception) {
                throw java.lang.RuntimeException(e)
            }

        //获取TrustManager
        private val trustManagers: Array<TrustManager>
            get() {
                return arrayOf(trustManager)
            }

        //获取HostnameVerifier
        val hostnameVerifier: HostnameVerifier
            get() {
                return HostnameVerifier { _, _ -> true }
            }

        val trustManager: X509TrustManager
            get() = MyTrustManager()

        @SuppressLint("TrustAllX509TrustManager", "CustomX509TrustManager")
        private class MyTrustManager : X509TrustManager {
            @Throws(CertificateException::class)
            override fun checkClientTrusted(
                chain: Array<X509Certificate>, authType: String
            ) {
            }

            @Throws(CertificateException::class)
            override fun checkServerTrusted(
                chain: Array<X509Certificate>, authType: String
            ) {
            }

            override fun getAcceptedIssuers(): Array<X509Certificate?> {
                return arrayOfNulls(0)
            }
        }
    }
}
