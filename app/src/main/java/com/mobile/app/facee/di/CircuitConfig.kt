package com.mobile.app.facee.di

import com.mobile.app.facee.ui.screens.AppScreen
import com.mobile.app.facee.ui.screens.MainPresenter
import com.mobile.app.facee.ui.screens.MainScreen
import com.mobile.app.facee.ui.screens.MainUi
import com.mobile.app.facee.ui.screens.chat.ChatPresenter
import com.mobile.app.facee.ui.screens.chat.ChatUi
import com.mobile.app.facee.ui.screens.home.HomePresenter
import com.mobile.app.facee.ui.screens.home.HomeUi
import com.mobile.app.facee.ui.screens.match.MatchPresenter
import com.mobile.app.facee.ui.screens.match.MatchUi
import com.mobile.app.facee.ui.screens.message.MessagePresenter
import com.mobile.app.facee.ui.screens.message.MessageUi
import com.mobile.app.facee.ui.screens.mine.MinePresenter
import com.mobile.app.facee.ui.screens.mine.ProfileUi
import com.slack.circuit.foundation.Circuit

/**
 * 提供Circuit配置
 * 注册所有的Presenter和UI工厂
 */
fun provideCircuitConfig(): Circuit {
    return Circuit.Builder()
        .addPresenterFactory { screen, navigator, _ ->
            when (screen) {
                is MainScreen -> MainPresenter(navigator)
                is AppScreen.HomeScreen -> HomePresenter(navigator)
                is AppScreen.MatchScreen -> MatchPresenter(navigator)
                is AppScreen.MessageScreen -> MessagePresenter(navigator)
                is AppScreen.MineScreen -> MinePresenter(navigator)
                is AppScreen.ChatScreen -> ChatPresenter(navigator, screen.userId, screen.userName)
                else -> null
            }
        }
        .addUiFactory { screen, _ ->
            when (screen) {
                is MainScreen -> MainUi()
                is AppScreen.HomeScreen -> HomeUi()
                is AppScreen.MatchScreen -> MatchUi()
                is AppScreen.MessageScreen -> MessageUi()
                is AppScreen.MineScreen -> ProfileUi()
                is AppScreen.ChatScreen -> ChatUi()
                else -> null
            }
        }
        .build()
}