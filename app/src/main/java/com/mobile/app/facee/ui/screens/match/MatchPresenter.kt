package com.mobile.app.facee.ui.screens.match

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import com.mobile.app.facee.ui.screens.AppScreen
import com.slack.circuit.runtime.Navigator
import com.slack.circuit.runtime.presenter.Presenter

/**
 * 匹配页面Presenter
 * 负责管理匹配页面的状态和业务逻辑
 */
class MatchPresenter(
    private val navigator: Navigator
) : Presenter<MatchScreen.State> {

    @Composable
    override fun present(): MatchScreen.State {
        var isLoading by remember { mutableStateOf(false) }
        var currentUser by remember { mutableStateOf<MatchScreen.MatchUser?>(null) }
        var userQueue by remember { mutableStateOf(getSampleUsers()) }
        var hasMoreUsers by remember { mutableStateOf(true) }

        // 初始化当前用户
        if (currentUser == null && userQueue.isNotEmpty()) {
            currentUser = userQueue.first()
            userQueue = userQueue.drop(1)
        }

        return MatchScreen.State(
            isLoading = isLoading,
            currentUser = currentUser,
            userQueue = userQueue,
            hasMoreUsers = hasMoreUsers,
            eventSink = { event ->
                when (event) {
                    is MatchScreen.Event.LoadNextUser -> {
                        if (userQueue.isNotEmpty()) {
                            currentUser = userQueue.first()
                            userQueue = userQueue.drop(1)
                        } else {
                            // TODO: 加载更多用户
                            hasMoreUsers = false
                        }
                    }
                    is MatchScreen.Event.SwipeLeft -> {
                        // 不喜欢，加载下一个用户
                        if (userQueue.isNotEmpty()) {
                            currentUser = userQueue.first()
                            userQueue = userQueue.drop(1)
                        } else {
                            currentUser = null
                        }
                    }
                    is MatchScreen.Event.SwipeRight -> {
                        // 喜欢，检查是否匹配
                        val isMatch = checkMatch(event.userId)
                        if (isMatch) {
                            // TODO: 显示匹配成功弹窗
                        }
                        
                        if (userQueue.isNotEmpty()) {
                            currentUser = userQueue.first()
                            userQueue = userQueue.drop(1)
                        } else {
                            currentUser = null
                        }
                    }
                    is MatchScreen.Event.SuperLike -> {
                        // 超级喜欢
                        // TODO: 实现超级喜欢逻辑
                        if (userQueue.isNotEmpty()) {
                            currentUser = userQueue.first()
                            userQueue = userQueue.drop(1)
                        } else {
                            currentUser = null
                        }
                    }
                    is MatchScreen.Event.Refresh -> {
                        isLoading = true
                        // TODO: 重新加载用户列表
                        userQueue = getSampleUsers()
                        if (userQueue.isNotEmpty()) {
                            currentUser = userQueue.first()
                            userQueue = userQueue.drop(1)
                        }
                        hasMoreUsers = true
                        isLoading = false
                    }
                    is MatchScreen.Event.ViewProfile -> {
                        navigator.goTo(AppScreen.UserDetailScreen(event.userId))
                    }
                }
            }
        )
    }

    private fun checkMatch(userId: String): Boolean {
        // TODO: 实现真实的匹配检查逻辑
        // 这里简单模拟30%的匹配概率
        return (0..100).random() < 30
    }

    private fun getSampleUsers(): List<MatchScreen.MatchUser> {
        return listOf(
            MatchScreen.MatchUser(
                id = "match1",
                name = "小雨",
                age = 25,
                location = "北京",
                distance = 2,
                bio = "喜欢旅行和摄影，寻找有趣的灵魂",
                photos = listOf(
                    "https://example.com/photo1.jpg",
                    "https://example.com/photo2.jpg"
                ),
                interests = listOf("旅行", "摄影", "美食"),
                occupation = "设计师",
                education = "本科"
            ),
            MatchScreen.MatchUser(
                id = "match2",
                name = "阳光男孩",
                age = 28,
                location = "上海",
                distance = 5,
                bio = "健身爱好者，喜欢户外运动",
                photos = listOf(
                    "https://example.com/photo3.jpg",
                    "https://example.com/photo4.jpg"
                ),
                interests = listOf("健身", "跑步", "篮球"),
                occupation = "工程师",
                education = "硕士"
            ),
            MatchScreen.MatchUser(
                id = "match3",
                name = "文艺青年",
                age = 26,
                location = "深圳",
                distance = 8,
                bio = "爱读书，爱音乐，爱生活",
                photos = listOf(
                    "https://example.com/photo5.jpg"
                ),
                interests = listOf("阅读", "音乐", "电影"),
                occupation = "编辑",
                education = "本科"
            )
        )
    }
}
