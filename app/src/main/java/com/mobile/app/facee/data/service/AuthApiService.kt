package com.mobile.app.facee.data.service

import com.mobile.app.facee.data.model.AuthBean
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * 认证相关API接口
 */
interface AuthApi {

    /**
     * 登录
     */
    @POST("/api/v1/user/login")
    suspend fun signIn(@Body request: SignInRequest): ApiResponse<AuthBean>

    /**
     * 刷新令牌
     */
    @POST("auth/refresh")
    suspend fun refreshToken(@Body request: RefreshTokenRequest): ApiResponse<AuthBean>

    /**
     * 登出
     */
    @POST("auth/logout")
    suspend fun logout(): ApiResponse<Unit>
}


/**
 * 登录请求
 */
data class SignInRequest(
    val auth_id: String, val auth_type: Int, val auth_token: String, val country_id: Int
)

/**
 * 刷新令牌请求
 */
data class RefreshTokenRequest(
    val refreshToken: String
)
