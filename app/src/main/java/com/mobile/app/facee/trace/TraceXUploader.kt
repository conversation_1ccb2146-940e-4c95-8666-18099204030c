package com.mobile.app.facee.trace

import com.mobile.app.facee.data.network.ApiResult
import com.mobile.app.facee.manager.FileUploadManager
import com.orhanobut.logger.Logger
import java.io.File

/**
 * 日志文件上传器
 * 负责将本地日志文件上传到服务器
 */
object TraceXUploader {

    /**
     * 兼容旧版API的上传方法
     */
    suspend fun upload(file: File, block: (Boolean) -> Unit) {
        try {
            Logger.d("LogUploader: 开始上传日志: ${file.name}")

            val result = FileUploadManager.uploadLog(file)

            when (result) {
                is ApiResult.Success -> {
                    if (result.data.success) {
                        Logger.d("LogUploader: 日志上传成功: ${file.name}")
                    } else {
                        Logger.e("LogUploader: 日志上传失败: ${result.data.errorMessage}")
                    }
                    block.invoke(result.data.success)
                }

                is ApiResult.Error -> {
                    block.invoke(false)
                    Logger.e("LogUploader: 日志上传失败: ${result.message}")
                }

                is ApiResult.Loading -> {
                    block.invoke(false)
                    Logger.d("LogUploader: 日志上传超时")
                }
            }
        } catch (e: Exception) {
            Logger.e("LogUploader: 日志上传异常: ${e.message}")
            block.invoke(false)
        }
    }
}
