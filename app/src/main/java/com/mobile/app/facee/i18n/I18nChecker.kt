package com.mobile.app.facee.i18n

import android.content.Context
import com.mobile.app.facee.trace.TraceX


data class I18nBean(
    val mapping: Map<String, String>?,
    val default_lang: String?,
    val lang: Map<String, Map<String, String>>,
    val md5: String?
)

object I18n<PERSON>he<PERSON> {

    suspend fun checkAndUpdateLanguage(context: Context): <PERSON><PERSON><PERSON> {
        return runCatching {
//            withContext(Dispatchers.IO) {
//                val result =
//                    RetrofitClient.aggregatedService.getLangInfo(I18nManager.getLangMd5(context))
//                if (result.isSuccess()) {
//                    TraceX.d("Language info fetched: ${result.data}")
//                    I18nManager.saveLangMd5(context, result.data?.md5 ?: "")
//                    result.data?.let { config ->
//                        config.lang.forEach { (langCode, langData) ->
//                            I18nManager.saveLangJson(context, langCode, langData.toJson(), 1)
//                        }
//                    }
//                }
//                result.isSuccess() && result.data != null
//            }
            false
        }.getOrElse {
            TraceX.e("Failed to update language: ${it.message}")
            false
        }
    }
}
