package com.mobile.app.facee.data.network

import com.mobile.app.facee.trace.LogX
import okhttp3.Interceptor
import okhttp3.MediaType
import okhttp3.Request
import okhttp3.Response
import okio.Buffer
import java.io.IOException
import java.nio.charset.Charset
import java.util.UUID
import java.util.concurrent.TimeUnit

/**
 * 详细日志拦截器
 * 记录详细的请求头、请求体、响应头、响应体信息
 */
class DetailedLogInterceptor : Interceptor {

    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val startTime = System.nanoTime()

        // 生成唯一的请求ID
        val requestId = UUID.randomUUID().toString().substring(0, 8)

        val response: Response
        try {
            response = chain.proceed(originalRequest)

            // 记录实际发送的请求信息（从response中获取）
            val actualRequest = response.request
            logRequest(actualRequest, requestId)

        } catch (e: Exception) {
            LogX.e("HTTP请求异常 [RequestID: $requestId]: ${e.message}")
            // 即使出错也记录请求信息
            logRequest(originalRequest, requestId)
            throw e
        }

        val endTime = System.nanoTime()
        val duration = TimeUnit.NANOSECONDS.toMillis(endTime - startTime)

        // 记录响应信息
        logResponse(response.request, response, duration, requestId)

        return response
    }

    /**
     * 记录请求信息
     */
    private fun logRequest(request: Request, requestId: String) {
        try {
            val sb = StringBuilder()
            sb.append("\n")
            sb.append("╔══════════════════════════════════════════════════════════════════════════════════════\n")
            sb.append("║ HTTP REQUEST [ID: $requestId]\n")
            sb.append("╠══════════════════════════════════════════════════════════════════════════════════════\n")
            sb.append("║ Method: ${request.method}\n")
            sb.append("║ URL: ${request.url}\n")
            sb.append("║ Content-Type: ${request.body?.contentType()?.toString() ?: "N/A"}\n")
            sb.append("║ Content-Length: ${request.body?.contentLength()?.let { if (it >= 0) "$it bytes" else "Unknown" } ?: "N/A"}\n")
            sb.append("║ \n")
            sb.append("║ Headers:\n")

            // 记录请求头
            if (request.headers.size > 0) {
                for (i in 0 until request.headers.size) {
                    val name = request.headers.name(i)
                    val value = request.headers.value(i)
                    sb.append("║   $name: $value\n")
                }
            } else {
                sb.append("║   [No headers]\n")
            }

            // 记录请求体
            request.body?.let { body ->
                sb.append("║ \n")
                sb.append("║ Request Body:\n")
                if (isParseable(body.contentType())) {
                    val requestBuffer = Buffer()
                    body.writeTo(requestBuffer)
                    var charset = Charset.forName("UTF-8")
                    body.contentType()?.let { contentType ->
                        charset = contentType.charset(charset) ?: charset
                    }
                    val bodyString = requestBuffer.readString(charset)
                    if (JsonFormatter.isJson(bodyString)) {
                        sb.append("║   [JSON Format]\n")
                        sb.append("${JsonFormatter.formatJsonWithPrefix(bodyString)}\n")
                    } else {
                        sb.append("║   $bodyString\n")
                    }
                } else {
                    sb.append("║   [Binary content, size: ${body.contentLength()} bytes]\n")
                }
            }

            sb.append("╚══════════════════════════════════════════════════════════════════════════════════════\n")

            LogX.d(sb.toString())
        } catch (e: Exception) {
            LogX.e("记录请求日志失败 [RequestID: $requestId]: ${e.message}")
        }
    }

    /**
     * 记录响应信息
     */
    private fun logResponse(request: Request, response: Response, duration: Long, requestId: String) {
        try {
            val sb = StringBuilder()
            sb.append("\n")
            sb.append("╔══════════════════════════════════════════════════════════════════════════════════════\n")
            sb.append("║ HTTP RESPONSE [ID: $requestId]\n")
            sb.append("╠══════════════════════════════════════════════════════════════════════════════════════\n")
            sb.append("║ URL: ${request.url}\n")
            sb.append("║ Code: ${response.code} ${response.message}\n")
            sb.append("║ Duration: ${duration}ms\n")
            sb.append("║ Success: ${response.isSuccessful}\n")
            sb.append("║ Protocol: ${response.protocol}\n")
            sb.append("║ Content-Type: ${response.body?.contentType()?.toString() ?: "N/A"}\n")
            sb.append("║ Content-Length: ${response.body?.contentLength()?.let { if (it >= 0) "$it bytes" else "Unknown" } ?: "N/A"}\n")

            // 记录TraceId
            response.headers["traceparent"]?.let { traceId ->
                sb.append("║ TraceId: $traceId\n")
            }
            
            sb.append("║ \n")
            sb.append("║ Response Headers:\n")

            // 记录响应头
            if (response.headers.size > 0) {
                for (i in 0 until response.headers.size) {
                    val name = response.headers.name(i)
                    val value = response.headers.value(i)
                    sb.append("║   $name: $value\n")
                }
            } else {
                sb.append("║   [No headers]\n")
            }

            // 记录响应体
            response.body?.let { body ->
                sb.append("║ \n")
                sb.append("║ Response Body:\n")
                if (isParseable(body.contentType())) {
                    val source = body.source()
                    source.request(Long.MAX_VALUE)
                    val buffer = source.buffer
                    
                    var charset = Charset.forName("UTF-8")
                    body.contentType()?.let { contentType ->
                        charset = contentType.charset(charset) ?: charset
                    }
                    
                    val bodyString = buffer.clone().readString(charset)
                    if (JsonFormatter.isJson(bodyString)) {
                        sb.append("║   [JSON Format]\n")
                        sb.append("${JsonFormatter.formatJsonWithPrefix(bodyString)}\n")
                    } else {
                        sb.append("║   $bodyString\n")
                    }
                } else {
                    sb.append("║   [Binary content, size: ${body.contentLength()} bytes]\n")
                }
            }

            sb.append("╚══════════════════════════════════════════════════════════════════════════════════════\n")

            LogX.d(sb.toString())
        } catch (e: Exception) {
            LogX.e("记录响应日志失败 [RequestID: $requestId]: ${e.message}")
        }
    }

    /**
     * 判断内容类型是否可解析
     */
    private fun isParseable(mediaType: MediaType?): Boolean {
        return when {
            mediaType?.type == null -> false
            mediaType.type == "text" -> true
            mediaType.subtype.contains("json", ignoreCase = true) -> true
            mediaType.subtype.contains("xml", ignoreCase = true) -> true
            mediaType.subtype.contains("html", ignoreCase = true) -> true
            mediaType.subtype.contains("x-www-form-urlencoded", ignoreCase = true) -> true
            mediaType.subtype.contains("plain", ignoreCase = true) -> true
            else -> false
        }
    }
}
