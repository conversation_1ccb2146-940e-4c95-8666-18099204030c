package com.mobile.app.facee.i18n

import android.R
import android.content.Context
import android.content.res.TypedArray
import android.util.AttributeSet
import com.mobile.app.facee.trace.TraceX

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025/6/27 18:30
 * @description: 国际化视图助手类，提供通用的国际化功能
 */
class I18nViewHelper(
    private val context: Context,
    private val textSetter: (String) -> Unit
) {
    
    private var textResourceId: Int = -1

    /**
     * 初始化国际化功能
     */
    fun initialize(attrs: AttributeSet?) {
        // 尝试从 AttributeSet 中获取资源 ID
        attrs?.let { attributeSet ->
            textResourceId = getTextResourceIdFromAttrs(attributeSet)
        }
    }

    /**
     * 处理文本国际化
     */
    fun processText(currentText: String?) {
        try {
            if (!currentText.isNullOrEmpty()) {
                // 优先使用从 AttributeSet 获取的资源 ID
                val resourceId = textResourceId

                if (resourceId != -1) {
                    // 如果找到了资源 ID，直接通过资源名查找翻译
                    val resourceName = context.resources.getResourceEntryName(resourceId)

                    if (I18nManager.has(resourceName)) {
                        val translation = I18nManager.get(resourceName)

                        // 检查是否是格式化字符串
                        val finalText = if (isFormatString(translation)) {
                            // 如果是格式化字符串但没有参数，保持原样
                            // 实际使用时应该通过 setTextWithArgs 来设置
                            processEscapeSequences(translation)
                        } else {
                            processEscapeSequences(translation)
                        }

                        textSetter(finalText)
                        return
                    } else {
//                        TraceX.i("I18nViewHelper", "🔄 No translation found for resource: '$resourceName'")
                    }
                }
            }
        } catch (e: Exception) {
            TraceX.e("I18nViewHelper", "🔄 Error processing text: ${e.message}")
        }
    }

    /**
     * 处理带格式化参数的文本国际化
     */
    fun processTextWithArgs(resourceName: String, vararg args: Any) {
        try {
            if (I18nManager.has(resourceName)) {
                val template = I18nManager.get(resourceName)
                try {
                    val formattedText = String.format(template, *args)
                    val processedText = processEscapeSequences(formattedText)
                    textSetter(processedText)
                } catch (e: Exception) {
                    TraceX.e("I18nViewHelper", "🔄 Format error: ${e.message}, using template: '$template'")
                    val processedTemplate = processEscapeSequences(template)
                    textSetter(processedTemplate)
                }
            } else {
                // 没有翻译，尝试从系统资源获取
                try {
                    val resourceId = context.resources.getIdentifier(resourceName, "string", context.packageName)
                    if (resourceId != 0) {
                        val template = context.getString(resourceId)
                        val formattedText = String.format(template, *args)
                        val processedText = processEscapeSequences(formattedText)
                        textSetter(processedText)
                    } else {
                        TraceX.w("I18nViewHelper", "🔄 Resource not found: '$resourceName'")
                        textSetter(resourceName)
                    }
                } catch (e: Exception) {
                    TraceX.e("I18nViewHelper", "🔄 Error getting system resource: ${e.message}")
                    textSetter(resourceName)
                }
            }
        } catch (e: Exception) {
            TraceX.e("I18nViewHelper", "🔄 Error processing text with args: ${e.message}")
        }
    }

    /**
     * 检查字符串是否包含格式化占位符
     */
    private fun isFormatString(text: String): Boolean {
        return text.contains("%") && (text.contains("%s") || text.contains("%d") || text.contains("%1\$"))
    }

    /**
     * 处理转义序列，将字面字符串转换为实际的转义字符
     */
    private fun processEscapeSequences(text: String): String {
        return text
            .replace("\\n", "\n")        // 换行符
            .replace("\\t", "\t")        // 制表符
            .replace("\\r", "\r")        // 回车符
            .replace("\\\"", "\"")       // 双引号
            .replace("\\'", "'")         // 单引号
            .replace("\\\\", "\\")       // 反斜杠
    }

    companion object {
        /**
         * 处理 TypedArray.getString() 的国际化
         * 用法：I18nViewHelper.getStringFromTypedArray(typedArray, R.styleable.CustomView_text)
         */
        @JvmStatic
        fun getStringFromTypedArray(context: Context, typedArray: TypedArray, index: Int): String? {
            return try {
                // 先尝试获取资源ID
                val resourceId = typedArray.getResourceId(index, -1)
                if (resourceId != -1) {
                    // 如果是资源引用，获取资源名并查找翻译
                    val resourceName = context.resources.getResourceEntryName(resourceId)
                    if (I18nManager.has(resourceName)) {
                        val translation = I18nManager.get(resourceName)
                        return processEscapeSequencesStatic(translation)
                    }
                }

                // 如果没有翻译，使用原始字符串
                val originalString = typedArray.getString(index)
                originalString?.let { processEscapeSequencesStatic(it) }
            } catch (e: Exception) {
                TraceX.e("I18nViewHelper", "🔄 Error getting string from TypedArray: ${e.message}")
                typedArray.getString(index)
            }
        }

        /**
         * 静态版本的转义序列处理
         */
        @JvmStatic
        private fun processEscapeSequencesStatic(text: String): String {
            return text
                .replace("\\n", "\n")
                .replace("\\t", "\t")
                .replace("\\r", "\r")
                .replace("\\\"", "\"")
                .replace("\\'", "'")
                .replace("\\\\", "\\")
        }
    }

    /**
     * 处理资源 ID 设置的文本
     */
    fun processResourceText(resId: Int): String? {
        return try {
            if (resId != 0) {
                val resourceName = context.resources.getResourceEntryName(resId)
                if (I18nManager.has(resourceName)) {
                    val translation = I18nManager.get(resourceName)
                    return translation
                }
            }
            null
        } catch (e: Exception) {
            TraceX.d("I18nViewHelper", "🔄 Resource text translation failed: ${e.message}")
            null
        }
    }

    /**
     * 从 AttributeSet 中获取 android:text 的资源 ID
     */
    private fun getTextResourceIdFromAttrs(attrs: AttributeSet): Int {
        return try {
            val typedArray = context.obtainStyledAttributes(attrs, intArrayOf(R.attr.text))
            val resourceId = typedArray.getResourceId(0, -1)
            typedArray.recycle()
//            if (resourceId != -1) {
//                TraceX.i("I18nViewHelper", "🔄 Found text resource ID from attrs: $resourceId")
//            }
            resourceId
        } catch (e: Exception) {
            TraceX.d("I18nViewHelper", "🔄 Failed to get resource ID from attrs: ${e.message}")
            -1
        }
    }
}
