package com.mobile.app.facee.manager

import com.mobile.app.facee.trace.LogX
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

/**
 * 通话状态管理器：用于标记当前是否正在通话中
 */
object CallStateManager {
    // 是否正在通话中
    private val isCalling = AtomicBoolean(false)

    // 使用 AtomicInteger 确保线程安全
    private val currentCallId = AtomicInteger(0)

    /**
     * 原子性地尝试开始通话
     * @param channelID 通话ID
     * @return true 如果成功开始通话，false 如果已经在通话中
     */
    fun tryStartCall(channelID: Int): Boolean {
        // 使用 compareAndSet 确保原子性操作
        val success = isCalling.compareAndSet(false, true)
        if (success) {
            currentCallId.set(channelID)
            LogX.i("CallStateManager, 成功开始通话状态，callID: $channelID")
        } else {
            LogX.i("CallStateManager, 通话已在进行中，无法开始新通话，当前callID: ${currentCallId.get()}")
        }
        return success
    }

    /**
     * 设置通话中状态（保留原有方法以兼容现有代码）
     */
    fun startCall(channelID: Int) {
        LogX.i("CallStateManager, 开始通话状态")
        isCalling.set(true)
        currentCallId.set(channelID)
    }

    /**
     * 清除通话状态
     */
    fun endCall() {
        LogX.i("CallStateManager, 重置通话状态")
        isCalling.set(false)
        currentCallId.set(0)
    }

    /**
     * 是否正在通话
     */
    fun isInCall(): Boolean {
        return isCalling.get()
    }

    fun getCurrentCallId(): Int = currentCallId.get()
}
