package com.mobile.app.facee.ui.screens.mine

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import com.mobile.app.facee.ui.screens.AppScreen
import com.slack.circuit.runtime.Navigator
import com.slack.circuit.runtime.presenter.Presenter

/**
 * 个人中心Presenter
 * 负责管理个人中心的状态和业务逻辑
 */
class MinePresenter(
    private val navigator: Navigator
) : Presenter<MineScreen.State> {

    @Composable
    override fun present(): MineScreen.State {
        var isLoading by remember { mutableStateOf(false) }
        var userProfile by remember { mutableStateOf(getSampleProfile()) }

        return MineScreen.State(
            isLoading = isLoading,
            userProfile = userProfile,
            eventSink = { event ->
                when (event) {
                    is MineScreen.Event.EditProfile -> {
                        // TODO: 导航到编辑资料页面
                    }
                    is MineScreen.Event.ViewSettings -> {
                        navigator.goTo(AppScreen.SettingsScreen)
                    }
                    is MineScreen.Event.ViewMatches -> {
                        // TODO: 导航到匹配历史页面
                    }
                    is MineScreen.Event.ViewLikes -> {
                        // TODO: 导航到喜欢我的人页面
                    }
                    is MineScreen.Event.Logout -> {
                        // TODO: 实现登出逻辑
                    }
                    is MineScreen.Event.UpdatePhoto -> {
                        userProfile?.let { profile ->
                            userProfile = profile.copy(
                                photos = profile.photos + event.photoUrl
                            )
                        }
                    }
                }
            }
        )
    }

    private fun getSampleProfile(): MineScreen.UserProfile {
        return MineScreen.UserProfile(
            id = "current_user",
            name = "我的昵称",
            age = 25,
            location = "北京",
            bio = "热爱生活，喜欢旅行和摄影。寻找有趣的灵魂一起探索世界。",
            photos = listOf(
                "https://example.com/my_photo1.jpg",
                "https://example.com/my_photo2.jpg",
                "https://example.com/my_photo3.jpg"
            ),
            interests = listOf("旅行", "摄影", "美食", "音乐", "电影", "健身"),
            occupation = "产品经理",
            education = "本科",
            height = "170cm",
            zodiacSign = "天秤座",
            matchCount = 23,
            likeCount = 156,
            superLikeCount = 8
        )
    }
}
