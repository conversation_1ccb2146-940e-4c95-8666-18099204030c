package com.mobile.app.facee.net.interception

import com.facee.netlibrary.utils.NetKey
import com.mobile.app.facee.net.ApiQueryInfo
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException


/**
 *
 * @Author： Lxf
 * @Time： 2022/2/17 3:45 下午
 * @description： 自定义头部参数拦截器，传入heads
 */
class NetHeadInterceptor : Interceptor {
    private val apiQueryInfo by lazy { ApiQueryInfo() }

    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        var builder = chain.request().newBuilder()
        val getBuilder = chain.request().url.newBuilder()
        if ("GET" == chain.request().method.uppercase()) {
//            getBuilder.addQueryParameter(NetKey.KEY_VERSION, "1.0")
//            getBuilder.addQueryParameter(NetKey.KEY_USER_ID, "")
            builder = builder.url(getBuilder.build()).build().newBuilder()
        }

        //添加公共参数
        getBuilder.apply {
//            addQueryParameter(NetKey.KEY_MODEL, apiQueryInfo.model)
//            addQueryParameter(NetKey.KEY_OS_TYPE, "1")
//            addQueryParameter(
//                NetKey.KEY_DEVICE_ID, UniqueIDUtil.getUniqueID(AppManager.getApplication())
//            )
//            addQueryParameter(NetKey.KEY_DEVICE_MODEL, Build.MODEL)
//            addQueryParameter(NetKey.KEY_OS_VERSION, Build.VERSION.RELEASE)
//            addQueryParameter(
//                NetKey.KEY_PACKAGE_NAME,
//                        BuildConfig.APPLICATION_ID
//            )
//            addQueryParameter(NetKey.KEY_VERSION, "${BuildConfig.VERSION_CODE}")
//            addQueryParameter(
//                NetKey.KEY_APP_ID, MMKVDataRep.userInfo.app_id.toString()
//            )
//            addQueryParameter(
//                NetKey.KEY_LANG, Locale.getDefault().language
//            )
//
//            if (MMKVDataRep.userInfo.id.isNotEmpty()) {
//                addQueryParameter(
//                    "uid", MMKVDataRep.userInfo.id
//                )
//            }
        }

        //添加header
        builder.run {
            runCatching {
                addHeader(NetKey.KEY_CONTENT_TYPE, "application/json")
//                addHeader(NetKey.KEY_ACCESS_TOKEN, MMKVBaseDataRep.token ?: "")
//                addHeader(NetKey.KEY_VERSION_CODE, BuildConfig.VERSION_CODE.toString())
//                addHeader(NetKey.KEY_VERSION_NAME, BuildConfig.VERSION_NAME)
//                addHeader(NetKey.KEY_PKG, BuildConfig.APPLICATION_ID)
//                addHeader(NetKey.KEY_MODEL, Build.MODEL)
//                addHeader(
//                    NetKey.KEY_DEVICEID, UniqueIDUtil.getUniqueID(AppManager.getApplication())
//                )
//                addHeader(NetKey.KEY_DEVICETYPE, "android")
//                addHeader(NetKey.KEY_APPSFLYER_APPID, MMKVGuiYinDataRep.fetchAppsflyerAppId())
//                addHeader(NetKey.KEY_FIREBASE_APPID, MMKVGuiYinDataRep.fetchFirebaseAppId())
//                addHeader(NetKey.KEY_GOOGLE_GAID, MMKVGuiYinDataRep.fetchGoogleAdvertisingId())
            }
        }

        val request = builder.url(getBuilder.build()).build()
        return chain.proceed(request)
    }

}