package com.mobile.app.facee.ui.screens.home

import com.slack.circuit.runtime.CircuitUiEvent
import com.slack.circuit.runtime.CircuitUiState

/**
 * 首页屏幕 - 动态展示和推荐用户
 */
object HomeScreen {

    /**
     * 首页状态
     */
    data class State(
        val isLoading: Boolean = false,
        val tabIndex: Int = 0,
        val posts: List<Post> = emptyList(),
        val recommendedUsers: List<User> = emptyList(),
        val eventSink: (Event) -> Unit
    ) : CircuitUiState

    /**
     * 首页事件
     */
    sealed interface Event : CircuitUiEvent {
        data object Refresh : Event
        data object LoadMore : Event
        data class LikePost(val postId: String) : Event
        data class CommentPost(val postId: String) : Event
        data class ViewUser(val userId: String) : Event
        data class FollowUser(val userId: String) : Event
    }

    /**
     * 动态数据模型
     */
    data class Post(
        val id: String,
        val userId: String,
        val userName: String,
        val userAvatar: String,
        val content: String,
        val images: List<String> = emptyList(),
        val likeCount: Int = 0,
        val commentCount: Int = 0,
        val isLiked: Boolean = false,
        val timestamp: Long
    )

    /**
     * 用户数据模型
     */
    data class User(
        val id: String,
        val name: String,
        val avatar: String,
        val age: Int,
        val location: String,
        val bio: String,
        val isFollowed: Boolean = false
    )
}
