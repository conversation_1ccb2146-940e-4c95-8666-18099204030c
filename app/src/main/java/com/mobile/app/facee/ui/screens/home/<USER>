package com.mobile.app.facee.ui.screens.home

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import com.facee.netlibrary.tryAwait
import com.mobile.app.facee.AppConstants.PAGE_SIZE
import com.mobile.app.facee.data.model.TabBean
import com.mobile.app.facee.data.service.UserApiService
import com.mobile.app.facee.net.NetDelegates
import com.mobile.app.facee.trace.LogX
import com.mobile.app.facee.ui.screens.AppScreen
import com.slack.circuit.runtime.Navigator
import com.slack.circuit.runtime.presenter.Presenter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * 首页Presenter
 * 负责管理首页的状态和业务逻辑
 */
class HomePresenter(
    private val navigator: Navigator
) : Presenter<HomeScreen.State> {

    val service: UserApiService by NetDelegates()

    @Composable
    override fun present(): HomeScreen.State {
        var isLoading by remember { mutableStateOf(false) }
        var posts by remember { mutableStateOf(getSamplePosts()) }
        var recommendedUsers by remember { mutableStateOf(getSampleUsers()) }

        val coroutineScope = rememberCoroutineScope()

        LaunchedEffect(Unit) {
            fetchTabList(coroutineScope) {
                fetchHomepage(coroutineScope)
            }
        }

        return HomeScreen.State(
            isLoading = isLoading,
            posts = posts,
            recommendedUsers = recommendedUsers,
            eventSink = { event ->
                when (event) {
                    is HomeScreen.Event.Refresh -> {
                        isLoading = true
                        // TODO: 实现刷新逻辑
                        isLoading = false
                    }

                    is HomeScreen.Event.LoadMore -> {
                        // TODO: 实现加载更多逻辑
                    }

                    is HomeScreen.Event.LikePost -> {
                        posts = posts.map { post ->
                            if (post.id == event.postId) {
                                post.copy(
                                    isLiked = !post.isLiked,
                                    likeCount = if (post.isLiked) post.likeCount - 1 else post.likeCount + 1
                                )
                            } else post
                        }
                    }

                    is HomeScreen.Event.CommentPost -> {
                        // TODO: 导航到评论页面
                        fetchHomepage(coroutineScope)
                    }

                    is HomeScreen.Event.ViewUser -> {
                        navigator.goTo(AppScreen.UserDetailScreen(event.userId))
                    }

                    is HomeScreen.Event.FollowUser -> {
                        recommendedUsers = recommendedUsers.map { user ->
                            if (user.id == event.userId) {
                                user.copy(isFollowed = !user.isFollowed)
                            } else user
                        }
                    }
                }
            })
    }

    private fun fetchTabList(coroutineScope: CoroutineScope, block: (List<TabBean>) -> Unit) {
        coroutineScope.launch {
            val response = service.getTabList().tryAwait {
                LogX.d("getTabList", "getTabList")
            }
            response?.list?.let {
                LogX.d("getTabList", "getTabList: $it")
                block.invoke(it)
            }
        }
    }

    private fun fetchHomepage(coroutineScope: CoroutineScope, type: Int = 0, cursor: String = "") {
        coroutineScope.launch {
            val response = service.getHomepage(type, cursor, PAGE_SIZE).tryAwait {

            }
            response?.let {

            }
        }
    }

    private fun getSamplePosts(): List<HomeScreen.Post> {
        return listOf(
            HomeScreen.Post(
                id = "1",
                userId = "user1",
                userName = "小美",
                userAvatar = "https://example.com/avatar1.jpg",
                content = "今天天气真好，出来拍照啦！",
                images = listOf("https://example.com/photo1.jpg"),
                likeCount = 12,
                commentCount = 3,
                timestamp = System.currentTimeMillis()
            ), HomeScreen.Post(
                id = "2",
                userId = "user2",
                userName = "阳光男孩",
                userAvatar = "https://example.com/avatar2.jpg",
                content = "刚刚健身完，感觉棒极了！💪",
                likeCount = 8,
                commentCount = 1,
                timestamp = System.currentTimeMillis() - 3600000
            )
        )
    }

    private fun getSampleUsers(): List<HomeScreen.User> {
        return listOf(
            HomeScreen.User(
                id = "rec1",
                name = "小雨",
                avatar = "https://example.com/rec1.jpg",
                age = 25,
                location = "北京",
                bio = "喜欢旅行和摄影"
            ), HomeScreen.User(
                id = "rec2",
                name = "大山",
                avatar = "https://example.com/rec2.jpg",
                age = 28,
                location = "上海",
                bio = "程序员，爱好音乐"
            )
        )
    }
}