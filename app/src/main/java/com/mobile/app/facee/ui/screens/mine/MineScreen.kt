package com.mobile.app.facee.ui.screens.mine

import com.slack.circuit.runtime.CircuitUiEvent
import com.slack.circuit.runtime.CircuitUiState

/**
 * 个人中心屏幕
 */
object MineScreen {

    /**
     * 个人中心状态
     */
    data class State(
        val isLoading: Boolean = false,
        val userProfile: UserProfile? = null,
        val eventSink: (Event) -> Unit
    ) : CircuitUiState

    /**
     * 个人中心事件
     */
    sealed interface Event : CircuitUiEvent {
        data object EditProfile : Event
        data object ViewSettings : Event
        data object ViewMatches : Event
        data object ViewLikes : Event
        data object Logout : Event
        data class UpdatePhoto(val photoUrl: String) : Event
    }

    /**
     * 用户资料数据模型
     */
    data class UserProfile(
        val id: String,
        val name: String,
        val age: Int,
        val location: String,
        val bio: String,
        val photos: List<String>,
        val interests: List<String>,
        val occupation: String,
        val education: String,
        val height: String = "",
        val zodiacSign: String = "",
        val matchCount: Int = 0,
        val likeCount: Int = 0,
        val superLikeCount: Int = 0
    )
}
