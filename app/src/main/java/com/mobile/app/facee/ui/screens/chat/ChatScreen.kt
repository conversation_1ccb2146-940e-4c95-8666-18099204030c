package com.mobile.app.facee.ui.screens.chat

import com.slack.circuit.runtime.CircuitUiEvent
import com.slack.circuit.runtime.CircuitUiState

/**
 * 聊天屏幕
 */
object ChatScreen {

    /**
     * 聊天页面状态
     */
    data class State(
        val isLoading: Boolean = false,
        val userId: String,
        val userName: String,
        val messages: List<ChatMessage> = emptyList(),
        val inputText: String = "",
        val isTyping: Boolean = false,
        val eventSink: (Event) -> Unit
    ) : CircuitUiState

    /**
     * 聊天页面事件
     */
    sealed interface Event : CircuitUiEvent {
        data object LoadMessages : Event
        data class SendMessage(val text: String) : Event
        data class UpdateInputText(val text: String) : Event
        data object Back : Event
        data object ViewProfile : Event
    }

    /**
     * 聊天消息数据模型
     */
    data class ChatMessage(
        val id: String,
        val senderId: String,
        val text: String,
        val timestamp: Long,
        val isFromMe: Boolean
    )
}
