package com.mobile.app.facee.ui.screens.auth

import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.fragment.app.FragmentActivity
import com.mobile.app.facee.data.network.ApiResult
import com.mobile.app.facee.di.AppModule
import com.mobile.app.facee.manager.GoogleSignInManager
import com.mobile.app.facee.trace.LogX
import com.mobile.app.facee.ui.screens.MainScreen
import com.slack.circuit.runtime.Navigator
import com.slack.circuit.runtime.presenter.Presenter
import kotlinx.coroutines.launch

/**
 * 登录页面Presenter
 * 负责处理登录相关的业务逻辑
 */
class LoginPresenter(
    private val navigator: Navigator
) : Presenter<LoginScreen.State> {

    // 创建依赖实例
    val authRepository = AppModule.provideAuthRepository()

    @Composable
    override fun present(): LoginScreen.State {
        val context = LocalContext.current
        val scope = rememberCoroutineScope()

        var isLoading by remember { mutableStateOf(false) }
        var isLoggedIn by remember { mutableStateOf(false) }
        var errorMessage by remember { mutableStateOf<String?>(null) }

        // Google登录管理器
        var googleSignInManager by remember { mutableStateOf<GoogleSignInManager?>(null) }

        // Google登录结果处理器
        val googleSignInLauncher = rememberLauncherForActivityResult(
            contract = ActivityResultContracts.StartActivityForResult()
        ) { result ->
            result.data?.let { intent ->
                googleSignInManager?.handleSignInResult(intent)
            }
        }

        // 初始化Google登录管理器
        LaunchedEffect(context) {
            if (context is FragmentActivity) {
                googleSignInManager = GoogleSignInManager(
                    context = context,
                    signInLauncher = googleSignInLauncher,
                    onSignInResult = { result ->
                        scope.launch {
                            when (result) {
                                is ApiResult.Success -> {
                                    isLoading = true
                                    val authResult = authRepository.googleSignIn(result.data)
                                    isLoading = false

                                    when (authResult) {
                                        is ApiResult.Success -> {
                                            LogX.d("登录成功")
                                            isLoggedIn = true
                                        }
                                        is ApiResult.Error -> {
                                            errorMessage = authResult.message ?: "登录失败"
                                            LogX.e("登录失败: ${errorMessage}")
                                        }
                                        is ApiResult.Loading -> {
                                            // 保持loading状态
                                        }
                                    }
                                }
                                is ApiResult.Error -> {
                                    errorMessage = result.message ?: "Google登录失败"
                                    LogX.e("Google登录失败: ${errorMessage}")
                                }
                                is ApiResult.Loading -> {
                                    // 通常不会到这里
                                }
                            }
                        }
                    }
                )
                googleSignInManager?.initialize(context)
            }
        }

        // 检查是否已经登录
        LaunchedEffect(Unit) {
            if (authRepository.isLoggedIn()) {
                isLoggedIn = true
            }
        }

        // 登录成功后导航到主页面
        LaunchedEffect(isLoggedIn) {
            if (isLoggedIn) {
                LogX.d("登录成功，导航到主页面 $navigator")
                navigator.goTo(MainScreen)
            }
        }

        return LoginScreen.State(
            isLoading = isLoading,
            isLoggedIn = isLoggedIn,
            errorMessage = errorMessage,
            eventSink = { event ->
                when (event) {
                    is LoginScreen.Event.GoogleSignIn -> {
                        if (!isLoading) {
                            LogX.d("开始Google登录")
                            googleSignInManager?.signIn()
                        }
                    }

                    is LoginScreen.Event.GuestSignIn -> {
                        if (!isLoading) {
                            LogX.d("开始游客登录")
                            scope.launch {
                                isLoading = true
                                val authResult = authRepository.guestSignIn()
                                isLoading = false

                                when (authResult) {
                                    is ApiResult.Success -> {
                                        LogX.d("游客登录成功")
                                        isLoggedIn = true
                                    }
                                    is ApiResult.Error -> {
                                        errorMessage = authResult.message
                                        LogX.e("Guest login failed: $errorMessage")
                                    }
                                    is ApiResult.Loading -> {
                                        // 保持loading状态
                                    }
                                }
                            }
                        }
                    }
                    is LoginScreen.Event.ClearError -> {
                        errorMessage = null
                    }
                    is LoginScreen.Event.NavigateToMain -> {
                        navigator.goTo(MainScreen)
                    }
                }
            }
        )
    }
}