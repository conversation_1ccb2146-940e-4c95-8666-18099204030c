package com.mobile.app.facee.data.network

import android.content.Context
import com.mobile.app.facee.BuildConfig
import com.mobile.app.facee.manager.DataStoreManager
import kotlinx.coroutines.runBlocking

/**
 * 网络配置管理器
 * 管理API环境、超时设置等网络相关配置
 * 使用 ActivityUtils 获取 Context
 */
object NetworkConfig {

    // 环境配置
    const val ENV_DEV = "dev"
    const val ENV_TEST = "test"
    const val ENV_STAGING = "staging"
    const val ENV_PROD = "prod"

    // 默认配置
    private const val DEFAULT_ENV = ENV_DEV
    private const val DEFAULT_TIMEOUT = 30L
    private const val DEFAULT_RETRY_COUNT = 3
    private const val DEFAULT_RETRY_DELAY = 1000L

    /**
     * 获取 Context
     */

    /**
     * API 环境配置
     */
    private val apiUrls = mapOf(
        ENV_DEV to BuildConfig.HOST,
        ENV_TEST to BuildConfig.HOST,
        ENV_STAGING to BuildConfig.HOST,
        ENV_PROD to BuildConfig.HOST
    )

    /**
     * 获取当前环境
     */
    fun getCurrentEnvironment(): String {
        return runBlocking { DataStoreManager.getString("api_environment") } ?: DEFAULT_ENV
    }

    /**
     * 设置当前环境
     */
    fun setEnvironment(environment: String) {
        if (apiUrls.containsKey(environment)) {
            runBlocking { DataStoreManager.putString("api_environment", environment) }
        }
    }

    /**
     * 获取当前API基础URL
     */
    fun getBaseUrl(): String {
        val env = getCurrentEnvironment()
        return apiUrls[env] ?: apiUrls[ENV_DEV]!!
    }

    /**
     * 获取连接超时时间（秒）
     */
    fun getConnectTimeout(): Long {
        return runBlocking { DataStoreManager.getLong("connect_timeout", DEFAULT_TIMEOUT) }
    }

    /**
     * 设置连接超时时间
     */
    fun setConnectTimeout(timeout: Long) {
        runBlocking { DataStoreManager.putLong("connect_timeout", timeout) }
    }

    /**
     * 获取读取超时时间（秒）
     */
    fun getReadTimeout(): Long {
        return runBlocking { DataStoreManager.getLong("read_timeout", DEFAULT_TIMEOUT) }
    }

    /**
     * 设置读取超时时间
     */
    fun setReadTimeout(timeout: Long) {
        runBlocking { DataStoreManager.putLong("read_timeout", timeout) }
    }

    /**
     * 获取写入超时时间（秒）
     */
    fun getWriteTimeout(): Long {
        return runBlocking { DataStoreManager.getLong("write_timeout", DEFAULT_TIMEOUT) }
    }

    /**
     * 设置写入超时时间
     */
    fun setWriteTimeout(timeout: Long) {
        runBlocking { DataStoreManager.putLong("write_timeout", timeout) }
    }

    /**
     * 获取重试次数
     */
    fun getRetryCount(): Int {
        return runBlocking { DataStoreManager.getInt("retry_count", DEFAULT_RETRY_COUNT) }
    }

    /**
     * 设置重试次数
     */
    fun setRetryCount(count: Int) {
        runBlocking { DataStoreManager.putInt("retry_count", count) }
    }

    /**
     * 获取重试延迟时间（毫秒）
     */
    fun getRetryDelay(): Long {
        return runBlocking { DataStoreManager.getLong("retry_delay", DEFAULT_RETRY_DELAY) }
    }

    /**
     * 设置重试延迟时间
     */
    fun setRetryDelay(delay: Long) {
        runBlocking { DataStoreManager.putLong("retry_delay", delay) }
    }

    /**
     * 是否启用日志
     */
    fun isLoggingEnabled(): Boolean {
        return runBlocking { DataStoreManager.getBoolean("network_logging", true) }
    }

    /**
     * 设置是否启用日志
     */
    fun setLoggingEnabled(enabled: Boolean) {
        runBlocking { DataStoreManager.putBoolean("network_logging", enabled) }
    }

    /**
     * 是否启用缓存
     */
    fun isCacheEnabled(): Boolean {
        return runBlocking { DataStoreManager.getBoolean("network_cache", true) }
    }

    /**
     * 设置是否启用缓存
     */
    fun setCacheEnabled(enabled: Boolean) {
        runBlocking { DataStoreManager.putBoolean("network_cache", enabled) }
    }

    /**
     * 获取缓存大小（字节）
     */
    fun getCacheSize(): Long {
        return runBlocking { DataStoreManager.getLong("cache_size", 10 * 1024 * 1024) } // 默认10MB
    }

    /**
     * 设置缓存大小
     */
    fun setCacheSize(size: Long) {
        runBlocking { DataStoreManager.putLong("cache_size", size) }
    }

    /**
     * 获取所有可用环境
     */
    fun getAvailableEnvironments(): List<String> {
        return apiUrls.keys.toList()
    }

    /**
     * 获取环境显示名称
     */
    fun getEnvironmentDisplayName(env: String): String {
        return when (env) {
            ENV_DEV -> "开发环境"
            ENV_TEST -> "测试环境"
            ENV_STAGING -> "预发布环境"
            ENV_PROD -> "生产环境"
            else -> "未知环境"
        }
    }

    /**
     * 是否为生产环境
     */
    fun isProductionEnvironment(): Boolean {
        return getCurrentEnvironment() == ENV_PROD
    }

    /**
     * 重置为默认配置
     */
    fun resetToDefault() {
        setEnvironment(DEFAULT_ENV)
        setConnectTimeout(DEFAULT_TIMEOUT)
        setReadTimeout(DEFAULT_TIMEOUT)
        setWriteTimeout(DEFAULT_TIMEOUT)
        setRetryCount(DEFAULT_RETRY_COUNT)
        setRetryDelay(DEFAULT_RETRY_DELAY)
        setLoggingEnabled(true)
        setCacheEnabled(true)
    }

    /**
     * 获取配置摘要
     */
    fun getConfigSummary(): Map<String, Any> {
        return mapOf(
            "environment" to getCurrentEnvironment(),
            "baseUrl" to getBaseUrl(),
            "connectTimeout" to getConnectTimeout(),
            "readTimeout" to getReadTimeout(),
            "writeTimeout" to getWriteTimeout(),
            "retryCount" to getRetryCount(),
            "retryDelay" to getRetryDelay(),
            "loggingEnabled" to isLoggingEnabled(),
            "cacheEnabled" to isCacheEnabled(),
            "cacheSize" to getCacheSize()
        )
    }
}
