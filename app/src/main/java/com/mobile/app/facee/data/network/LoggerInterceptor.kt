package com.mobile.app.facee.data.network

import com.mobile.app.facee.BuildConfig
import com.mobile.app.facee.trace.LogX
import com.mobile.app.facee.utils.JsonUtil
import okhttp3.FormBody
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import okhttp3.ResponseBody
import okio.Buffer
import okio.GzipSource
import java.nio.charset.Charset
import java.nio.charset.StandardCharsets

/**
 * Copyright (C), 2019-2021, 中传互动（湖北）信息技术有限公司
 * Author: HeChao
 * Date: 2021/6/8 16:45
 * Description: 公共参数拦截器
 */
class LoggerInterceptor : Interceptor {
    @Throws(Exception::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val startTime = System.currentTimeMillis()
        val request: Request = chain.request()
        val response: Response = chain.proceed(chain.request())
        val body: ResponseBody? = response.body
        if (BuildConfig.DEBUG && body != null) {/*大于10M不打印日志*/
            if (body.contentLength() > 5 * 1000 * 1000) {
                return response
            }
            val endTime = System.currentTimeMillis()
            val duration = endTime - startTime
            val sb = kotlin.text.StringBuilder()
            for (i in 0 until request.headers.size) {
                val name = request.headers.name(i)
                if ("Host" != name && "Connection" != name && "Accept-Encoding" != name && "User-Agent" != name) {
                    sb.append(name + "=" + request.headers.value(i) + ",")
                }
            }
            sb.append("traceId = ${response.headers["traceparent"]} ")
//            if (sb.length > 1) {
//                sb.deleteCharAt(sb.lastIndex)
//            }
            val method = request.method
            val requestParams = "RequestUrl: {$method ${request.url}}\n| RequestTag: {${
                request.toString().substring(
                    request.toString().indexOf("tags={") + 6, request.toString().length - 2
                )
            }}"
            var params =
                "\n-----------------------------------------Start-----------------------------------------\n| $requestParams\n| RequestHeaders: \n{ $sb }\n"
            if ("POST" == method) {
                if (request.body is FormBody) {
                    val requestBody = request.body as FormBody
                    sb.delete(0, sb.length)
                    for (i in 0 until requestBody.size) {
                        sb.append(requestBody.encodedName(i) + "=" + requestBody.encodedValue(i) + ",")
                    }
                    sb.delete(sb.length - 1, sb.length)
                    params += "| RequestParams:{ $sb }\n"
                } else {
                    val requestBody = request.body
                    requestBody?.let {
                        val buffer = Buffer()
                        it.writeTo(buffer)
                        val contentType = it.contentType()
                        val charset: Charset = contentType?.charset(Charset.forName("UTF-8"))
                            ?: Charset.forName("UTF-8")
                        params += if (isPlaintext(buffer)) {
                            "| RequestParams: ${buffer.readString(charset)}\n"
                        } else {
                            "| RequestParams: Binary data (${it.contentLength()} bytes)\n"
                        }
                    }
                }
            }
            var content = ""
            val source = body.source()
            try {
                val headers = response.headers
                source.request(Long.MAX_VALUE) // Buffer the entire body.
                var buffer = source.buffer
                if ("gzip".equals(headers["Content-Encoding"], ignoreCase = true)) {
                    GzipSource(buffer.clone()).use { gzippedResponseBody ->
                        buffer = Buffer()
                        buffer.writeAll(gzippedResponseBody)
                    }
                }

                val contentType = body.contentType()
                val charset: Charset =
                    contentType?.charset(StandardCharsets.UTF_8) ?: StandardCharsets.UTF_8

                if (body.contentLength() != 0L) {
                    content = buffer.clone().readString(charset)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }

            LogX.d(
                "HTTP-Request", "$params| Response:\n" + JsonUtil.formatJson(
                    content
                ) + "\n" + "--------------------------------------End: $duration ms--------------------------------------"
            )
        }
        return response
    }

    private fun isPlaintext(buffer: Buffer): Boolean {
        try {
            val prefix = Buffer()
            val byteCount = if (buffer.size < 64) buffer.size else 64
            buffer.copyTo(prefix, 0, byteCount)
            for (i in 0..15) {
                if (prefix.exhausted()) {
                    break
                }
                val codePoint = prefix.readUtf8CodePoint()
                if (Character.isISOControl(codePoint) && !Character.isWhitespace(codePoint)) {
                    return false
                }
            }
            return true
        } catch (e: Exception) {
            return false
        }
    }
}