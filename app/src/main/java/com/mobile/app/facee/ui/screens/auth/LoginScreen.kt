package com.mobile.app.facee.ui.screens.auth

import com.slack.circuit.runtime.CircuitUiEvent
import com.slack.circuit.runtime.CircuitUiState
import com.slack.circuit.runtime.screen.Screen
import kotlinx.parcelize.Parcelize

/**
 * 登录屏幕
 */
@Parcelize
data object LoginScreen : Screen {

    /**
     * 登录页面状态
     */
    data class State(
        val isLoading: Boolean = false,
        val isLoggedIn: Boolean = false,
        val errorMessage: String? = null,
        val eventSink: (Event) -> Unit
    ) : CircuitUiState

    enum class SignInType(val value: Int) {
        GUEST(1), GOOGLE(4)
    }

    /**
     * 登录页面事件
     */
    sealed interface Event : CircuitUiEvent {
        data object GoogleSignIn : Event
        data object GuestSignIn : Event
        data object ClearError : Event
        data object NavigateToMain : Event
    }
}