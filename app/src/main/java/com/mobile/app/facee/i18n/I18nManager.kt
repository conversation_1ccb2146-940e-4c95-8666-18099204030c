package com.mobile.app.facee.i18n

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.mobile.app.facee.trace.TraceX
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.json.JSONObject
import java.util.Locale
import java.util.concurrent.ConcurrentHashMap
import androidx.datastore.preferences.core.Preferences as DataStorePreferences

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2025/6/5 13:57
 * @description: 基于DataStore的高性能I18n管理器
 */

// DataStore扩展属性
private val Context.languageDataStore: DataStore<DataStorePreferences> by preferencesDataStore(name = "language_store")
private val Context.versionDataStore: DataStore<DataStorePreferences> by preferencesDataStore(name = "version_store")

object I18nManager {
    // 协程作用域
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // 翻译数据
    private var translations: Map<String, String> = emptyMap()
    private var fallback: Map<String, String> = emptyMap()
    private var currentLangCode: String = "en"
    private var isInitialized = false

    // 内存缓存，提升访问速度
    private val translationCache = ConcurrentHashMap<String, Map<String, String>>()
    private val versionCache = ConcurrentHashMap<String, Int>()

    // 线程安全
    private val translationMutex = Mutex()
    private val initMutex = Mutex()

    // 上下文引用
    private var appContext: Context? = null

    // DataStore Keys
    private fun langKey(langCode: String) = stringPreferencesKey("lang_$langCode")
    private fun versionKey(langCode: String) = intPreferencesKey("version_$langCode")

    /**
     * 初始化I18n管理器（完整初始化：加载缓存翻译 + 异步更新语言数据）
     */
    fun init(context: Context) {
        scope.launch {
            initMutex.withLock {
                if (isInitialized) return@withLock

                appContext = context
                val sysLang = Locale.getDefault().language
                currentLangCode = sysLang

                TraceX.i("I18nManager", "🚀 Initializing I18nManager with system language: $sysLang")

                // 1. 立即加载缓存的翻译数据（优先保证功能可用）
                loadTranslations(sysLang)
                isInitialized = true

                TraceX.i("I18nManager", "✅ I18nManager initialized with cached data")
            }
        }
    }

    /**
     * 异步更新语言数据（不阻塞主流程）
     */
    fun updateLanguageDataAsync(context: Context) {
        scope.launch {
            try {
                TraceX.i("I18nManager", "🔄 Starting async language data update...")

                // 调用语言接口获取最新数据
                val success = I18nChecker.checkAndUpdateLanguage(context)

                if (success) {
                    TraceX.i("I18nManager", "✅ Language data updated successfully")

                    // 重新加载翻译数据（使用最新的数据）
                    val sysLang = Locale.getDefault().language
                    loadTranslations(sysLang)

                    TraceX.i("I18nManager", "🔄 Translations reloaded with updated data")
                } else {
                    TraceX.w(
                        "I18nManager", "⚠️ Language API call failed, continuing with cached data"
                    )
                }
            } catch (e: Exception) {
                TraceX.e("I18nManager", "❌ Async language update failed: ${e.message}")
                // 失败不影响主流程，继续使用缓存数据
            }
        }
    }

    /**
     * 加载翻译数据（按照需求逻辑：系统语言 -> 英文fallback）
     */
    private suspend fun loadTranslations(langCode: String) {
        TraceX.i("I18nManager", "loadTranslations $langCode $appContext")
        appContext?.let { context ->
            translationMutex.withLock {
                try {
                    TraceX.i("I18nManager", "🔄 Loading translations for system language: $langCode")

                    // 1. 尝试加载系统语言的翻译
                    val currentTranslations = loadLangJson(context, langCode)

                    if (currentTranslations != null && currentTranslations.isNotEmpty()) {
                        // 找到了系统语言的翻译
                        TraceX.i(
                            "I18nManager",
                            "✅ Found translations for $langCode: ${currentTranslations.size} entries"
                        )
                        translations = currentTranslations

                        // 加载英语作为fallback（如果当前语言不是英语）
                        fallback = if (langCode != "en") {
                            loadLangJson(context, "en") ?: emptyMap()
                        } else {
                            emptyMap()
                        }
                    } else {
                        // 没有找到系统语言的翻译，使用英语作为默认
                        val englishTranslations = loadLangJson(context, "en") ?: emptyMap()

                        if (englishTranslations.isNotEmpty()) {
                            translations = englishTranslations
                            fallback = emptyMap()
                        } else {
                            TraceX.e("I18nManager", "❌ No English translations found either!")
                            translations = emptyMap()
                            fallback = emptyMap()
                        }
                    }

                    currentLangCode = langCode
                    TraceX.i(
                        "I18nManager",
                        "🎯 Final state: ${translations.size} main translations, ${fallback.size} fallback translations"
                    )
                } catch (e: Exception) {
                    TraceX.e(
                        "I18nManager", "❌ Failed to load translations for $langCode: ${e.message}"
                    )
                    // 确保有基本的状态
                    translations = emptyMap()
                    fallback = emptyMap()
                }
            }
        }
    }

    /**
     * 加载语言JSON数据（内部使用）
     */
    private suspend fun loadLangJson(context: Context, langCode: String): Map<String, String>? {
        val langCode = when (langCode) {
            "id", "in", "id_" -> "id_"
            else -> langCode
        }
        // 先检查内存缓存
        translationCache[langCode]?.let {
            TraceX.d("Language loaded from cache: $langCode")
            return it
        }

        return try {
            val json = context.languageDataStore.data.map { preferences: DataStorePreferences ->
                preferences[langKey(langCode)]
            }.first()

            if (json != null) {
                TraceX.d("Language found in DataStore: $langCode $json")
                val translations = parseJsonToMap(json)
                if (translations != null) {
                    // 缓存结果
                    translationCache[langCode] = translations
                    TraceX.d("Language loaded from DataStore: $langCode, entries: ${translations.size}")
                    translations
                } else {
                    TraceX.w("Failed to parse JSON for language: $langCode")
                    null
                }
            } else {
                TraceX.d("No data found for language: $langCode")
                null
            }
        } catch (e: Exception) {
            TraceX.e("Failed to load language $langCode from DataStore: ${e.message}")
            null
        }
    }

    /**
     * 保存语言JSON数据
     */
    suspend fun saveLangJson(context: Context, langCode: String, json: String, version: Int) {
        try {
            // 保存翻译数据
            context.languageDataStore.edit { preferences ->
                preferences[langKey(langCode)] = json
            }

            // 保存版本信息
            context.versionDataStore.edit { preferences ->
                preferences[versionKey(langCode)] = version
            }

            // 更新缓存
            val translations = parseJsonToMap(json)
            if (translations != null) {
                translationCache[langCode] = translations
                versionCache[langCode] = version
                TraceX.d("Language saved to DataStore: $langCode, version: $version, entries: ${translations.size}")
            }

        } catch (e: Exception) {
            TraceX.e("Failed to save language $langCode to DataStore: ${e.message}")
            throw e
        }
    }

    /**
     * 获取语言版本
     */
    suspend fun getLangVersion(context: Context, langCode: String): Int {
        // 先检查内存缓存
        versionCache[langCode]?.let { return it }

        return try {
            val version = context.versionDataStore.data.map { preferences: DataStorePreferences ->
                preferences[versionKey(langCode)] ?: 0
            }.first()

            // 缓存结果
            versionCache[langCode] = version
            version
        } catch (e: Exception) {
            TraceX.e("Failed to get version for $langCode from DataStore: ${e.message}")
            0
        }
    }

    suspend fun saveLangMd5(context: Context, md5: String) {
        try {
            context.versionDataStore.edit { preferences ->
                preferences[stringPreferencesKey("md5")] = md5
            }
        } catch (e: Exception) {
            TraceX.e("Failed to save md5 to DataStore: ${e.message}")
            throw e
        }
    }

    suspend fun getLangMd5(context: Context): String {
        return try {
            context.versionDataStore.data.map { preferences: DataStorePreferences ->
                preferences[stringPreferencesKey("md5")] ?: ""
            }.first()
        } catch (e: Exception) {
            TraceX.e("Failed to get md5 from DataStore: ${e.message}")
            ""
        }
    }

    /**
     * 获取翻译文本
     */
    fun get(key: String): String {
        val translation = translations[key]
        if (translation != null) {
            return translation
        }

        val fallbackTranslation = fallback[key]
        if (fallbackTranslation != null) {
            return fallbackTranslation
        }

        return "[$key]"
    }

    /**
     * 检查是否有指定key的翻译
     */
    fun has(key: String): Boolean {
        val hasTranslation = translations.containsKey(key) || fallback.containsKey(key)
        return hasTranslation
    }

    /**
     * 添加测试翻译数据（用于调试）
     */
    fun addTestTranslations() {
        TraceX.i("I18nManager", "🔧 Adding test translations...")
        translations = mapOf(
            "app_name" to "心动合并 (测试翻译)",
            "current_ver" to "当前版本: %1\$s (测试翻译)",
            "app_summary" to "这是一个测试翻译的应用摘要\\n通过 I18nTextView 实现！\\n支持换行符处理",
            "copyright" to "版权所有 © 2025 心动合并\\n(测试翻译)",
            "setting_about_us" to "关于我们\\n(测试翻译)",
            "first_recharge_text_tips" to "首次充值 %1\$s 获得 %2\$s 钻石\\n(测试翻译)",
            "user_level" to "用户等级: %1\$d 级 (测试翻译)",
            "welcome_message" to "欢迎 %1\$s，\\n今天是 %2\$s\\n(测试翻译)",
            "multiline_text" to "第一行文本\\n第二行文本\\n第三行文本",
            "vip_recharge_activity" to "vip_recharge_activity"
        )
        isInitialized = true
        TraceX.i("I18nManager", "✅ Test translations added: ${translations.size} entries")
        TraceX.i("I18nManager", "Test translations: $translations")
    }

    /**
     * 切换语言
     */
    fun applyNewLang(context: Context, langCode: String) {
        if (currentLangCode == langCode) {
            TraceX.d("Language already set to $langCode, skipping")
            return
        }

        TraceX.d("Switching language to: $langCode")

        scope.launch {
            loadTranslations(langCode)
            TraceX.d("Language switched to $langCode successfully")
        }
    }

    /**
     * 获取当前语言
     */
    fun getCurrentLang(): String = currentLangCode

    /**
     * 预加载语言
     */
    fun preloadLanguage(langCode: String) {
        appContext?.let { context ->
            scope.launch {
                loadLangJson(context, langCode)
                TraceX.d("Preloaded language: $langCode")
            }
        }
    }

    /**
     * 解析JSON字符串为Map
     */
    private fun parseJsonToMap(json: String): Map<String, String>? {
        return try {
            val obj = JSONObject(json)
            obj.keys().asSequence().associateWith { obj.getString(it) }
        } catch (e: Exception) {
            TraceX.e("Failed to parse JSON: ${e.message}")
            null
        }
    }

    /**
     * 清除指定语言的缓存
     */
    fun clearCache(langCode: String) {
        translationCache.remove(langCode)
        versionCache.remove(langCode)
        TraceX.d("Cache cleared for language: $langCode")
    }

    /**
     * 清除所有缓存
     */
    fun clearAllCache() {
        translationCache.clear()
        versionCache.clear()
        TraceX.d("All language cache cleared")
    }

    /**
     * 公开的加载语言JSON数据方法（用于兼容性）
     */
    suspend fun loadLangJsonPublic(context: Context, langCode: String): Map<String, String>? {
        return loadLangJson(context, langCode)
    }

    // ==================== 性能优化方法 ====================

    /**
     * 性能优化：分批保存翻译数据到DataStore
     */
    suspend fun saveTranslationsBatch(
        context: Context, translationsMap: Map<String, String>, batchSize: Int = 50
    ) {
        try {
            TraceX.i(
                "I18nManager",
                "🔧 Saving ${translationsMap.size} translations in batches of $batchSize..."
            )

            translationsMap.entries.chunked(batchSize).forEachIndexed { index, batch ->
                context.languageDataStore.edit { preferences ->
                    batch.forEach { (key, value) ->
                        preferences[stringPreferencesKey(key)] = value
                    }
                }

                // 避免阻塞主线程，每5批次暂停一下
                if (index % 5 == 0) {
                    delay(10)
                }
            }

            // 更新内存缓存（需要创建新的可变Map）
            val newTranslations = translations.toMutableMap()
            translationsMap.forEach { (key, value) ->
                newTranslations[key] = value
            }
            translations = newTranslations

            TraceX.i("I18nManager", "✅ All translations saved successfully in batches")
        } catch (e: Exception) {
            TraceX.e("I18nManager", "❌ Failed to save translations: ${e.message}")
        }
    }

    /**
     * 性能优化：按优先级加载翻译
     */
    suspend fun loadTranslationsByPriority(context: Context, priority: String) {
        try {
            val priorityKeys = getKeysByPriority(priority)
            TraceX.i(
                "I18nManager",
                "🔧 Loading $priority priority translations: ${priorityKeys.size} keys"
            )

            context.languageDataStore.data.first().let { preferences ->
                val newTranslations = translations.toMutableMap()
                priorityKeys.forEach { key ->
                    preferences[stringPreferencesKey(key)]?.let { value ->
                        newTranslations[key] = value
                    }
                }
                translations = newTranslations
            }

            TraceX.i("I18nManager", "✅ Loaded $priority priority translations")
        } catch (e: Exception) {
            TraceX.e("I18nManager", "❌ Failed to load $priority translations: ${e.message}")
        }
    }

    /**
     * 根据优先级获取翻译键
     */
    private fun getKeysByPriority(priority: String): List<String> {
        return when (priority) {
            "critical" -> listOf("app_name", "loading", "error_network", "error_unknown")
            "high" -> listOf("login", "register", "main_menu", "settings", "setting_about_us")
            "normal" -> listOf("about", "help", "profile", "logout", "app_summary", "copyright")
            "low" -> listOf("terms", "privacy", "feedback", "current_ver")
            else -> emptyList()
        }
    }

    /**
     * 性能优化：清理内存缓存
     */
    fun clearMemoryCache() {
        translations = mutableMapOf()
        TraceX.i("I18nManager", "🧹 Memory cache cleared")
    }

    /**
     * 性能优化：获取内存使用情况
     */
    fun getMemoryUsage(): String {
        val size = translations.size
        val estimatedMemory =
            translations.entries.sumOf { it.key.length + it.value.length } * 2 // 粗略估算字节数
        return "Translations: $size entries, ~${estimatedMemory / 1024}KB"
    }
}
