package com.mobile.app.facee.data.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class CallHistoryBean(
    @Json(name = "callId") val callId: String = "", //通话ID
    @<PERSON><PERSON>(name = "anchor_id") val anchorId: String = "",
    @<PERSON><PERSON>(name = "anchorNickName") val nickName: String = "",
    @<PERSON>son(name = "anchor_avatar") val anchorAvatar: String = "", //主播头像
    @<PERSON>son(name = "callDurationSecond") val callTime: Long = 0, //通话秒数
    @<PERSON><PERSON>(name = "connectAt") val createTime: Long = 0L,
    @<PERSON><PERSON>(name = "call_stat") val callStat: String = "", //1未接通 2已接通
    @<PERSON>son(name = "status") val status: String = "", //1在线 2通话中 3离线 4勿扰
    @<PERSON><PERSON>(name = "coin_total") val coilTotal: Int = 0,  //货币总数
)