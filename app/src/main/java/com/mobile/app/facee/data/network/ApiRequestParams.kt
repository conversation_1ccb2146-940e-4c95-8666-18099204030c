package com.mobile.app.facee.data.network

import android.content.Context
import android.os.Build
import com.mobile.app.facee.BuildConfig
import com.mobile.app.facee.manager.DataStoreManager
import com.mobile.app.facee.utils.AppContextHolder
import com.mobile.app.facee.utils.UniqueIDUtil
import kotlinx.coroutines.runBlocking

/**
 * API请求参数
 * 每个API请求都需要携带的固定参数
 */
data class ApiRequestParams(
    val appID: String? = null,
    val cursor: String? = null,
    val deviceID: String? = null,
    val deviceModel: String? = null,
    val lang: String? = null,
    val osType: String? = null,
    val osVersion: String? = null,
    val packageName: String? = null,
    val size: Int? = null,
    val uid: String,
    val version: String? = null
)

/**
 * API参数构建器
 * 负责生成和管理所有API请求的通用参数
 * 使用 ActivityUtils 获取 Context
 */
object ApiParamsBuilder {

    // 默认参数常量
    private const val DEFAULT_APP_ID = "1"
    private const val DEFAULT_OS_TYPE = "1"
    private const val DEFAULT_LANG = "en"
    private const val DEFAULT_PAGE_SIZE = 20

    /**
     * 获取 Context
     */
    private fun getContext(): Context {
        return AppContextHolder.getApplication()
    }

    /**
     * 获取设备唯一标识
     */
    private fun getDeviceID(): String {
        var deviceId = runBlocking { DataStoreManager.getString("device_id") }
        if (deviceId.isNullOrEmpty()) {
            deviceId = UniqueIDUtil.getUniqueID(getContext())
            runBlocking { DataStoreManager.putString("device_id", deviceId) }
        }
        return deviceId
    }

    /**
     * 获取用户ID
     */
    private fun getUID(): String {
        return DataStoreManager.getUserId() ?: ""
    }

    /**
     * 获取设备型号
     */
    private fun getDeviceModel(): String {
        return "${Build.MANUFACTURER} ${Build.MODEL}"
    }

    /**
     * 获取系统版本
     */
    private fun getOSVersion(): String {
        return Build.VERSION.RELEASE
    }

    /**
     * 获取应用包名
     */
    private fun getPackageName(): String {
        return getContext().packageName
    }

    /**
     * 获取应用版本
     */
    private fun getAppVersion(): String {
        return BuildConfig.VERSION_CODE.toString()
    }

    /**
     * 获取语言设置
     */
    private fun getLanguage(): String {
        return DataStoreManager.getCurrentLanguage()
    }

    /**
     * 创建通用参数
     */
    fun createCommonParams(
        additionalParams: Map<String, Any> = emptyMap()
    ): Map<String, Any> {
        val commonParams = mutableMapOf<String, Any>()

        // 添加固定参数
        commonParams["appID"] = DataStoreManager.getAppId()
        commonParams["deviceID"] = getDeviceID()
        commonParams["deviceModel"] = getDeviceModel()
        commonParams["lang"] = getLanguage()
        commonParams["osType"] = DEFAULT_OS_TYPE
        commonParams["osVersion"] = getOSVersion()
        commonParams["packageName"] = getPackageName()
        if (getUID().isNotEmpty()) {
            commonParams["uid"] = getUID()
        }
        commonParams["version"] = getAppVersion().toInt()

        // 添加额外参数
        commonParams.putAll(additionalParams)

        return commonParams
    }

    /**
     * 创建分页参数
     */
    fun createPageParams(
        cursor: String? = null, size: Int = DEFAULT_PAGE_SIZE
    ): Map<String, Any> {
        return createCommonParams()
    }

    /**
     * 更新用户ID
     */
    fun updateUID(uid: String) {
        runBlocking { DataStoreManager.putString(DataStoreManager.KEY_USER_ID, uid) }
    }

    /**
     * 更新语言设置
     */
    fun updateLanguage(lang: String) {
        runBlocking { DataStoreManager.putString("app_language", lang) }
    }

    /**
     * 获取当前用户ID
     */
    fun getCurrentUID(): String {
        return getUID()
    }

    /**
     * 获取当前设备ID
     */
    fun getCurrentDeviceID(): String {
        return getDeviceID()
    }
}
