package com.mobile.app.facee.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.PagerState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.lerp
import androidx.compose.ui.text.font.FontWeight
import com.mobile.app.facee.utils.Dpi
import com.mobile.app.facee.utils.Spi

@Composable
fun CustomTabRow(
    tabs: List<String>,
    selectedTabIndex: Int,
    pagerState: PagerState,
    onTabClick: (Int) -> Unit
) {
    // 定义颜色
    val selectedColor = Color.Black
    val unselectedColor = Color(0xFFAFB1B3)

    // 计算滑动进度和颜色渐变
    val currentPage = pagerState.currentPage
    val pageOffset = pagerState.currentPageOffsetFraction

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(end = 16.Dpi ),
        horizontalArrangement = Arrangement.Start
    ) {
        tabs.forEachIndexed { index, tab ->
            val isSelected = selectedTabIndex == index

            // 计算当前tab的颜色（考虑滑动渐变）
            val tabColor = remember(currentPage, pageOffset, index) {
                derivedStateOf {
                    when {
                        // 当前选中的tab
                        index == currentPage -> {
                            if (pageOffset == 0f) {
                                selectedColor
                            } else {
                                // 从选中色渐变到未选中色
                                lerp(selectedColor, unselectedColor, kotlin.math.abs(pageOffset))
                            }
                        }
                        // 即将选中的tab（滑动目标）
                        (index == currentPage + 1 && pageOffset > 0) ||
                                (index == currentPage - 1 && pageOffset < 0) -> {
                            // 从未选中色渐变到选中色
                            lerp(unselectedColor, selectedColor, kotlin.math.abs(pageOffset))
                        }
                        // 其他tab保持未选中色
                        else -> unselectedColor
                    }
                }
            }

            Text(
                text = tab,
                fontSize = 20.Spi,
                modifier = Modifier
                    .clickable { onTabClick(index) }
                    .padding(horizontal =  16.Dpi, vertical = 12.Dpi),
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                color = tabColor.value
            )
        }
    }
}