package com.mobile.app.facee.ui.screens

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.outlined.FavoriteBorder
import androidx.compose.material.icons.outlined.Home
import androidx.compose.material.icons.outlined.Person
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.outlined.Email
import androidx.compose.material3.Icon
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import com.slack.circuit.foundation.CircuitContent
import com.slack.circuit.runtime.ui.Ui

/**
 * 主屏幕UI组件
 * 包含底部导航栏和对应的屏幕内容
 */
class MainUi : Ui<MainScreen.State> {

    @Composable
    override fun Content(state: MainScreen.State, modifier: Modifier) {
        Scaffold(
            modifier = modifier.fillMaxSize(),
            bottomBar = {
                BottomNavigationBar(
                    selectedTab = state.selectedTab,
                    onTabSelected = { tab ->
                        state.eventSink(MainScreen.Event.TabSelected(tab))
                    }
                )
            }
        ) { paddingValues ->
            CircuitContent(
                screen = state.selectedTab.screen,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            )
        }
    }
}

@Composable
private fun BottomNavigationBar(
    selectedTab: MainScreen.TabItem,
    onTabSelected: (MainScreen.TabItem) -> Unit
) {
    NavigationBar {
        MainScreen.TabItem.entries.forEach { tab ->
            NavigationBarItem(
                icon = {
                    Icon(
                        imageVector = getTabIcon(tab, selectedTab == tab),
                        contentDescription = tab.title
                    )
                },
                label = { Text(tab.title) },
                selected = selectedTab == tab,
                onClick = { onTabSelected(tab) }
            )
        }
    }
}

private fun getTabIcon(tab: MainScreen.TabItem, isSelected: Boolean): ImageVector {
    return when (tab) {
        MainScreen.TabItem.HOME -> if (isSelected) Icons.Filled.Home else Icons.Outlined.Home
        MainScreen.TabItem.MATCH -> if (isSelected) Icons.Filled.Favorite else Icons.Outlined.FavoriteBorder
        MainScreen.TabItem.MESSAGE -> if (isSelected) Icons.Filled.Email else Icons.Outlined.Email
        MainScreen.TabItem.PROFILE -> if (isSelected) Icons.Filled.Person else Icons.Outlined.Person
    }
}
