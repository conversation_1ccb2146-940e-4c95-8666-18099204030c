package com.mobile.app.facee.ui.screens

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import com.mobile.app.facee.R
import com.slack.circuit.foundation.CircuitContent
import com.slack.circuit.runtime.ui.Ui

/**
 * 主屏幕UI组件
 * 包含底部导航栏和对应的屏幕内容
 */
class MainUi : Ui<MainScreen.State> {

    @Composable
    override fun Content(state: MainScreen.State, modifier: Modifier) {
        Scaffold(
            modifier = modifier.fillMaxSize(), bottomBar = {
                BottomNavigationBar(
                    selectedTab = state.selectedTab, onTabSelected = { tab ->
                        state.eventSink(MainScreen.Event.TabSelected(tab))
                    })
            }) { paddingValues ->
            CircuitContent(
                screen = state.selectedTab.screen,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            )
        }
    }
}

@Composable
private fun BottomNavigationBar(
    selectedTab: MainScreen.TabItem, onTabSelected: (MainScreen.TabItem) -> Unit
) {
    NavigationBar {
        MainScreen.TabItem.entries.forEach { tab ->
            NavigationBarItem(
                icon = {
                    Image(
                        painter = painterResource(getTabIcon(tab, selectedTab == tab)),
                        contentDescription = tab.title
                    )
            },
                alwaysShowLabel = false,
//                label = { Text(tab.title) },
                selected = selectedTab == tab,
                onClick = { onTabSelected(tab) })
        }
    }
}

private fun getTabIcon(tab: MainScreen.TabItem, isSelected: Boolean): Int {
    return when (tab) {
        MainScreen.TabItem.HOME -> if (isSelected) R.mipmap.ic_tab_home_active else R.mipmap.ic_tab_home_inactive
        MainScreen.TabItem.MATCH -> if (isSelected) R.mipmap.ic_tab_match_active else R.mipmap.ic_tab_match_inactive
        MainScreen.TabItem.MESSAGE -> if (isSelected) R.mipmap.ic_tab_message_active else R.mipmap.ic_tab_message_inactive
        MainScreen.TabItem.PROFILE -> if (isSelected) R.mipmap.ic_tab_mine_active else R.mipmap.ic_tab_mine_inactive
    }
}
