package com.mobile.app.facee.ui.screens.message

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import com.mobile.app.facee.ui.screens.AppScreen
import com.slack.circuit.runtime.Navigator
import com.slack.circuit.runtime.presenter.Presenter

/**
 * 消息页面Presenter
 * 负责管理消息页面的状态和业务逻辑
 */
class MessagePresenter(
    private val navigator: Navigator
) : Presenter<MessageScreen.State> {

    @Composable
    override fun present(): MessageScreen.State {
        var isLoading by remember { mutableStateOf(false) }
        var conversations by remember { mutableStateOf(getSampleConversations()) }
        var matches by remember { mutableStateOf(getSampleMatches()) }

        return MessageScreen.State(
            isLoading = isLoading,
            conversations = conversations,
            matches = matches,
            eventSink = { event ->
                when (event) {
                    is MessageScreen.Event.Refresh -> {
                        isLoading = true
                        // TODO: 实现刷新逻辑
                        conversations = getSampleConversations()
                        matches = getSampleMatches()
                        isLoading = false
                    }
                    is MessageScreen.Event.OpenChat -> {
                        navigator.goTo(AppScreen.ChatScreen(event.userId, event.userName))
                    }
                    is MessageScreen.Event.DeleteConversation -> {
                        conversations = conversations.filter { it.id != event.conversationId }
                    }
                    is MessageScreen.Event.MarkAsRead -> {
                        conversations = conversations.map { conversation ->
                            if (conversation.id == event.conversationId) {
                                conversation.copy(unreadCount = 0)
                            } else conversation
                        }
                    }
                }
            }
        )
    }

    private fun getSampleConversations(): List<MessageScreen.Conversation> {
        return listOf(
            MessageScreen.Conversation(
                id = "conv1",
                userId = "user1",
                userName = "小雨",
                userAvatar = "https://example.com/avatar1.jpg",
                lastMessage = "你好，很高兴认识你！",
                lastMessageTime = System.currentTimeMillis() - 300000, // 5分钟前
                unreadCount = 2,
                isOnline = true
            ),
            MessageScreen.Conversation(
                id = "conv2",
                userId = "user2",
                userName = "阳光男孩",
                userAvatar = "https://example.com/avatar2.jpg",
                lastMessage = "今天天气真好，要不要一起出去走走？",
                lastMessageTime = System.currentTimeMillis() - 3600000, // 1小时前
                unreadCount = 0,
                isOnline = false
            ),
            MessageScreen.Conversation(
                id = "conv3",
                userId = "user3",
                userName = "文艺青年",
                userAvatar = "https://example.com/avatar3.jpg",
                lastMessage = "刚看了一部很棒的电影，推荐给你",
                lastMessageTime = System.currentTimeMillis() - 7200000, // 2小时前
                unreadCount = 1,
                isOnline = true
            )
        )
    }

    private fun getSampleMatches(): List<MessageScreen.Match> {
        return listOf(
            MessageScreen.Match(
                id = "match1",
                userId = "newuser1",
                userName = "甜美女孩",
                userAvatar = "https://example.com/newavatar1.jpg",
                matchTime = System.currentTimeMillis() - 1800000, // 30分钟前
                hasStartedChat = false
            ),
            MessageScreen.Match(
                id = "match2",
                userId = "newuser2",
                userName = "运动达人",
                userAvatar = "https://example.com/newavatar2.jpg",
                matchTime = System.currentTimeMillis() - 5400000, // 1.5小时前
                hasStartedChat = false
            )
        )
    }
}
