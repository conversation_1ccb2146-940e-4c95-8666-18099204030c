package com.mobile.app.facee.ui.screens.message

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import com.facee.netlibrary.tryAwait
import com.mobile.app.facee.AppConstants
import com.mobile.app.facee.data.model.CallHistoryBean
import com.mobile.app.facee.data.model.TabBean
import com.mobile.app.facee.data.service.UserApiService
import com.mobile.app.facee.ui.screens.AppScreen
import com.mobile.app.facee.im.RongCloudAuthManager
import com.mobile.app.facee.net.NetDelegates
import com.mobile.app.facee.trace.LogX
import com.slack.circuit.runtime.Navigator
import com.slack.circuit.runtime.presenter.Presenter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * 消息页面Presenter
 * 负责管理消息页面的状态和业务逻辑
 */
class MessagePresenter(
    private val navigator: Navigator
) : Presenter<MessageScreen.State> {

    val service: UserApiService by NetDelegates()

    @Composable
    override fun present(): MessageScreen.State {
        var isLoading by remember { mutableStateOf(false) }
        var selectedTab by remember { mutableStateOf(MessageScreen.TabType.MESSAGE) }
        var conversations by remember { mutableStateOf(getSampleConversations()) }
        var matches by remember { mutableStateOf(getSampleMatches()) }
        var calls by remember { mutableStateOf(getSampleCalls()) }
        var follows by remember { mutableStateOf(getSampleFollows()) }
        var whoSeeMe by remember { mutableStateOf(getSampleWhoSeeMe()) }

        // 自动连接融云IM
        LaunchedEffect(Unit) {

        }

        return MessageScreen.State(
            isLoading = isLoading,
            selectedTab = selectedTab,
            conversations = conversations,
            matches = matches,
            calls = calls,
            follows = follows,
            whoSeeMe = whoSeeMe,
            eventSink = { event ->
                when (event) {
                    is MessageScreen.Event.TabSelected -> {
                        selectedTab = event.tab
                    }

                    is MessageScreen.Event.Refresh -> {
                        isLoading = true
                        // TODO: 实现刷新逻辑
                        when (selectedTab) {
                            MessageScreen.TabType.MESSAGE -> {
                                conversations = getSampleConversations()
                                matches = getSampleMatches()
                                whoSeeMe = getSampleWhoSeeMe()
                            }

                            MessageScreen.TabType.CALLS -> {
                                calls = getSampleCalls()
                            }

                            MessageScreen.TabType.FOLLOW -> {
                                follows = getSampleFollows()
                            }
                        }
                        isLoading = false
                    }

                    is MessageScreen.Event.LoadMore -> {
                        // TODO: 实现加载更多逻辑
                    }

                    is MessageScreen.Event.OpenChat -> {
                        navigator.goTo(AppScreen.ChatScreen(event.userId, event.userName))
                    }

                    is MessageScreen.Event.DeleteConversation -> {
                        conversations = conversations.filter { it.id != event.conversationId }
                    }

                    is MessageScreen.Event.MarkAsRead -> {
                        conversations = conversations.map { conversation ->
                            if (conversation.id == event.conversationId) {
                                conversation.copy(unreadCount = 0)
                            } else conversation
                        }
                    }

                    is MessageScreen.Event.CallUser -> {
                        // TODO: 实现通话逻辑
                    }

                    is MessageScreen.Event.FollowUser -> {
                        follows = follows.map { follow ->
                            if (follow.userId == event.userId) {
                                follow.copy(isFollowingBack = true)
                            } else follow
                        }
                    }

                    is MessageScreen.Event.UnfollowUser -> {
                        follows = follows.map { follow ->
                            if (follow.userId == event.userId) {
                                follow.copy(isFollowingBack = false)
                            } else follow
                        }
                    }

                    is MessageScreen.Event.ViewWhoSeeMe -> {
                        // TODO: 导航到用户详情页面
                    }
                }
            }
        )
    }

    /**
     * 获取通话历史消息
     */
    private fun fetchCallHistory(
        coroutineScope: CoroutineScope,
        cursor: String,
        block: (List<CallHistoryBean>) -> Unit
    ) {
        coroutineScope.launch {
            val response = service.getVideoHistory(cursor, AppConstants.PAGE_SIZE).tryAwait {
                LogX.d("getTabList", "getTabList")
            }
            response?.list?.let {
                LogX.d("getTabList", "getTabList: $it")
                block.invoke(it)
            }
        }
    }

    private fun getSampleConversations(): List<MessageScreen.Conversation> {
        return listOf(
            MessageScreen.Conversation(
                id = "conv1",
                userId = "user1",
                userName = "小雨",
                userAvatar = "https://example.com/avatar1.jpg",
                lastMessage = "你好，很高兴认识你！",
                lastMessageTime = System.currentTimeMillis() - 300000, // 5分钟前
                unreadCount = 2,
                isOnline = true
            ),
            MessageScreen.Conversation(
                id = "conv2",
                userId = "user2",
                userName = "阳光男孩",
                userAvatar = "https://example.com/avatar2.jpg",
                lastMessage = "今天天气真好，要不要一起出去走走？",
                lastMessageTime = System.currentTimeMillis() - 3600000, // 1小时前
                unreadCount = 0,
                isOnline = false
            ),
            MessageScreen.Conversation(
                id = "conv3",
                userId = "user3",
                userName = "文艺青年",
                userAvatar = "https://example.com/avatar3.jpg",
                lastMessage = "刚看了一部很棒的电影，推荐给你",
                lastMessageTime = System.currentTimeMillis() - 7200000, // 2小时前
                unreadCount = 1,
                isOnline = true
            )
        )
    }

    private fun getSampleMatches(): List<MessageScreen.Match> {
        return listOf(
            MessageScreen.Match(
                id = "match1",
                userId = "newuser1",
                userName = "甜美女孩",
                userAvatar = "https://example.com/newavatar1.jpg",
                matchTime = System.currentTimeMillis() - 1800000, // 30分钟前
                hasStartedChat = false
            ),
            MessageScreen.Match(
                id = "match2",
                userId = "newuser2",
                userName = "运动达人",
                userAvatar = "https://example.com/newavatar2.jpg",
                matchTime = System.currentTimeMillis() - 5400000, // 1.5小时前
                hasStartedChat = false
            )
        )
    }

    private fun getSampleCalls(): List<MessageScreen.CallRecord> {
        return listOf(
            MessageScreen.CallRecord(
                id = "call1",
                userId = "user1",
                userName = "小雨",
                userAvatar = "https://example.com/avatar1.jpg",
                callType = MessageScreen.CallType.VIDEO,
                callTime = System.currentTimeMillis() - 600000, // 10分钟前
                duration = 180, // 3分钟
                isIncoming = true,
                isMissed = false
            ),
            MessageScreen.CallRecord(
                id = "call2",
                userId = "user2",
                userName = "阳光男孩",
                userAvatar = "https://example.com/avatar2.jpg",
                callType = MessageScreen.CallType.VOICE,
                callTime = System.currentTimeMillis() - 3600000, // 1小时前
                duration = 0,
                isIncoming = false,
                isMissed = true
            ),
            MessageScreen.CallRecord(
                id = "call3",
                userId = "user3",
                userName = "文艺青年",
                userAvatar = "https://example.com/avatar3.jpg",
                callType = MessageScreen.CallType.VIDEO,
                callTime = System.currentTimeMillis() - 7200000, // 2小时前
                duration = 450, // 7.5分钟
                isIncoming = false,
                isMissed = false
            )
        )
    }

    private fun getSampleFollows(): List<MessageScreen.FollowUser> {
        return listOf(
            MessageScreen.FollowUser(
                id = "follow1",
                userId = "follow_user1",
                userName = "摄影师小李",
                userAvatar = "https://example.com/follow1.jpg",
                bio = "专业摄影师，喜欢记录生活中的美好瞬间",
                isOnline = true,
                followTime = System.currentTimeMillis() - 86400000, // 1天前
                isFollowingBack = false
            ),
            MessageScreen.FollowUser(
                id = "follow2",
                userId = "follow_user2",
                userName = "旅行达人",
                userAvatar = "https://example.com/follow2.jpg",
                bio = "环游世界是我的梦想，分享旅途中的故事",
                isOnline = false,
                followTime = System.currentTimeMillis() - 172800000, // 2天前
                isFollowingBack = true
            ),
            MessageScreen.FollowUser(
                id = "follow3",
                userId = "follow_user3",
                userName = "美食博主",
                userAvatar = "https://example.com/follow3.jpg",
                bio = "发现城市里的美味，分享料理心得",
                isOnline = true,
                followTime = System.currentTimeMillis() - 259200000, // 3天前
                isFollowingBack = false
            )
        )
    }

    private fun getSampleWhoSeeMe(): List<MessageScreen.WhoSeeMeUser> {
        return listOf(
            MessageScreen.WhoSeeMeUser(
                id = "who1",
                userId = "who_user1",
                userName = "神秘访客1",
                userAvatar = "https://example.com/who1.jpg",
                viewTime = System.currentTimeMillis() - 300000 // 5分钟前
            ),
            MessageScreen.WhoSeeMeUser(
                id = "who2",
                userId = "who_user2",
                userName = "神秘访客2",
                userAvatar = "https://example.com/who2.jpg",
                viewTime = System.currentTimeMillis() - 600000 // 10分钟前
            ),
            MessageScreen.WhoSeeMeUser(
                id = "who3",
                userId = "who_user3",
                userName = "神秘访客3",
                userAvatar = "https://example.com/who3.jpg",
                viewTime = System.currentTimeMillis() - 900000 // 15分钟前
            ),
            MessageScreen.WhoSeeMeUser(
                id = "who4",
                userId = "who_user4",
                userName = "神秘访客4",
                userAvatar = "https://example.com/who4.jpg",
                viewTime = System.currentTimeMillis() - 1200000 // 20分钟前
            ),
            MessageScreen.WhoSeeMeUser(
                id = "who5",
                userId = "who_user5",
                userName = "神秘访客5",
                userAvatar = "https://example.com/who5.jpg",
                viewTime = System.currentTimeMillis() - 1500000 // 25分钟前
            )
        )
    }
}
