package com.mobile.app.facee.ui.screens.auth

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withLink
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.mobile.app.facee.R
import com.mobile.app.facee.ui.theme.Black
import com.mobile.app.facee.ui.theme.Primary
import com.mobile.app.facee.ui.theme.White
import com.slack.circuit.runtime.ui.Ui

class LoginUi : Ui<LoginScreen.State> {

    @Composable
    override fun Content(
        state: LoginScreen.State, modifier: Modifier
    ) {
        LoginContent(
            state = state, modifier = modifier
        )
    }

    @Composable
    private fun LoginContent(
        state: LoginScreen.State, modifier: Modifier = Modifier
    ) {
        val snackBarHostState = remember { SnackbarHostState() }
        val signInType = remember { mutableStateOf<LoginScreen.SignInType?>(null) }

        // 显示错误消息
        LaunchedEffect(state.errorMessage) {
            state.errorMessage?.let { message ->
                snackBarHostState.showSnackbar(message)
                state.eventSink(LoginScreen.Event.ClearError)
                signInType.value = null
            }
        }

        Box(modifier = modifier.fillMaxSize()) {
            Image(
                painter = painterResource(R.mipmap.bg_login),
                contentDescription = "登录背景",
                contentScale = ContentScale.FillBounds,
                modifier = Modifier.fillMaxSize()
            )

            Card(
                colors = CardDefaults.cardColors(containerColor = White),
                shape = CardDefaults.shape,
                modifier = Modifier
                    .padding(start = 16.dp, end = 16.dp, bottom = 100.dp)
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
            ) {
                Column(modifier = Modifier.padding(24.dp)) {
                    AnimatedLoginButton(
                        onClick = {
                            if (!state.isLoading) {
                                state.eventSink(LoginScreen.Event.GoogleSignIn)
                            }
                            signInType.value = LoginScreen.SignInType.GOOGLE
                        },
                        enabled = !state.isLoading,
                        isLoading = state.isLoading,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(54.dp),
                        isOutlined = true
                    ) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            if (state.isLoading && signInType.value == LoginScreen.SignInType.GOOGLE) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(16.dp), strokeWidth = 2.dp
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("Sign in...")
                            } else {
                                Image(
                                    painter = painterResource(R.mipmap.ic_google),
                                    contentDescription = "Google",
                                    modifier = Modifier
                                        .padding(end = 5.dp)
                                        .size(16.dp)
                                )
                                Text("SIGN IN WITH GOOGLE")
                            }
                        }
                    }

                    Spacer(Modifier.height(20.dp))
                    AnimatedLoginButton(
                        onClick = {
                            if (!state.isLoading) {
                                state.eventSink(LoginScreen.Event.GuestSignIn)
                            }
                            signInType.value = LoginScreen.SignInType.GUEST
                        },
                        enabled = !state.isLoading,
                        isLoading = state.isLoading,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(54.dp),
                        isOutlined = false
                    ) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            if (state.isLoading && signInType.value == LoginScreen.SignInType.GUEST) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(16.dp), strokeWidth = 2.dp
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("Sign in...")
                            } else {
                                Image(
                                    painter = painterResource(R.mipmap.ic_guest),
                                    contentDescription = "Guest",
                                    modifier = Modifier
                                        .padding(end = 5.dp)
                                        .size(16.dp)
                                )
                                Text("SIGN IN AS GUEST")
                            }
                        }
                    }

                    Spacer(Modifier.height(40.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Image(
                            painter = painterResource(R.mipmap.ic_checkbox_checked),
                            contentDescription = "Guest",
                            colorFilter = ColorFilter.tint(Primary, blendMode = BlendMode.Plus),
                            modifier = Modifier
                                .padding(end = 5.dp)
                                .clip(CircleShape)
                                .size(16.dp)
                        )
                        Text(
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            text = buildAnnotatedString {
                                withStyle(style = SpanStyle(color = Black)) { append("Agree ") }
                                val termsOfUseLink = LinkAnnotation.Clickable(
                                    "Terms of use", styles = TextLinkStyles(
                                        style = SpanStyle(textDecoration = TextDecoration.Underline)
                                    )
                                ) {
                                    println("Clicked on link")
                                }
                                withLink(termsOfUseLink) {
                                    append("Terms of use")
                                }
                                withStyle(SpanStyle(color = Black)) { append(" and ") }
                                val privacyPolicyLink = LinkAnnotation.Clickable(
                                    "Privacy Policy", styles = TextLinkStyles(
                                        style = SpanStyle(textDecoration = TextDecoration.Underline)
                                    )
                                ) {
                                    println("Clicked on link")
                                }
                                withLink(privacyPolicyLink) {
                                    append("Privacy Policy")
                                }
                            })
                    }
                }
            }

            // Snackbar for error messages
            SnackbarHost(
                hostState = snackBarHostState, modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }

    @Composable
    @Preview
    fun Preview() {
        LoginContent(
            state = LoginScreen.State(
                isLoading = false, isLoggedIn = false, errorMessage = null, eventSink = {})
        )
    }

    /**
     * 带动画效果的登录按钮
     */
    @Composable
    private fun AnimatedLoginButton(
        onClick: () -> Unit,
        enabled: Boolean,
        isLoading: Boolean,
        modifier: Modifier = Modifier,
        isOutlined: Boolean = false,
        content: @Composable () -> Unit
    ) {
        val interactionSource = remember { MutableInteractionSource() }
        val isPressed by interactionSource.collectIsPressedAsState()

        // 按压动画：按下时缩小到0.95，松开时恢复到1.0
        val scale by animateFloatAsState(
            targetValue = if (isPressed) 0.95f else 1.0f,
            animationSpec = tween(durationMillis = 100),
            label = "button_scale"
        )

        if (isOutlined) {
            OutlinedButton(
                onClick = onClick, enabled = enabled, modifier = modifier.graphicsLayer {
                    scaleX = scale
                    scaleY = scale
                }, colors = ButtonDefaults.outlinedButtonColors(
                    containerColor = White, contentColor = Black
                ), interactionSource = interactionSource
            ) {
                content()
            }
        } else {
            Button(
                onClick = onClick, enabled = enabled, modifier = modifier.graphicsLayer {
                    scaleX = scale
                    scaleY = scale
                }, colors = ButtonDefaults.buttonColors(
                    containerColor = Black, contentColor = White
                ), interactionSource = interactionSource
            ) {
                content()
            }
        }
    }
}
