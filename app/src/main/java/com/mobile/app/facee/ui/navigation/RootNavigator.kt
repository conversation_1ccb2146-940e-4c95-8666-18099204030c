package com.mobile.app.facee.ui.navigation

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.slack.circuit.foundation.CircuitContent
import com.slack.circuit.runtime.screen.Screen

/**
 * 根导航组件
 * 负责渲染指定的屏幕
 */
@Composable
fun RootNavHost(startScreen: Screen) {
    CircuitContent(
        screen = startScreen,
        modifier = Modifier.fillMaxSize()
    )
}