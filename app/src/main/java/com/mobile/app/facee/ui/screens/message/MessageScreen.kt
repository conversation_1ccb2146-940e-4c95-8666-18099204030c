package com.mobile.app.facee.ui.screens.message

import com.slack.circuit.runtime.CircuitUiEvent
import com.slack.circuit.runtime.CircuitUiState

/**
 * 消息屏幕 - 聊天列表
 */
object MessageScreen {

    /**
     * 消息页面状态
     */
    data class State(
        val isLoading: Boolean = false,
        val conversations: List<Conversation> = emptyList(),
        val matches: List<Match> = emptyList(),
        val eventSink: (Event) -> Unit
    ) : CircuitUiState

    /**
     * 消息页面事件
     */
    sealed interface Event : CircuitUiEvent {
        data object Refresh : Event
        data class OpenChat(val userId: String, val userName: String) : Event
        data class DeleteConversation(val conversationId: String) : Event
        data class MarkAsRead(val conversationId: String) : Event
    }

    /**
     * 对话数据模型
     */
    data class Conversation(
        val id: String,
        val userId: String,
        val userName: String,
        val userAvatar: String,
        val lastMessage: String,
        val lastMessageTime: Long,
        val unreadCount: Int = 0,
        val isOnline: Boolean = false
    )

    /**
     * 匹配数据模型
     */
    data class Match(
        val id: String,
        val userId: String,
        val userName: String,
        val userAvatar: String,
        val matchTime: Long,
        val hasStartedChat: Boolean = false
    )
}
