package com.mobile.app.facee.ui.screens.message

import com.slack.circuit.runtime.CircuitUiEvent
import com.slack.circuit.runtime.CircuitUiState

/**
 * 消息屏幕 - 包含Message、Calls、Follow三个Tab
 */
object MessageScreen {

    /**
     * Tab类型枚举
     */
    enum class TabType(val title: String) {
        MESSAGE("Message"),
        CALLS("Calls"),
        FOLLOW("Follow")
    }

    /**
     * 消息页面状态
     */
    data class State(
        val isLoading: Boolean = false,
        val selectedTab: TabType = TabType.MESSAGE,
        val conversations: List<Conversation> = emptyList(),
        val matches: List<Match> = emptyList(),
        val calls: List<CallRecord> = emptyList(),
        val follows: List<FollowUser> = emptyList(),
        val whoSeeMe: List<WhoSeeMeUser> = emptyList(),
        val eventSink: (Event) -> Unit
    ) : CircuitUiState

    /**
     * 消息页面事件
     */
    sealed interface Event : CircuitUiEvent {
        data class TabSelected(val tab: TabType) : Event
        data object Refresh : Event
        data object LoadMore : Event
        data class OpenChat(val userId: String, val userName: String) : Event
        data class DeleteConversation(val conversationId: String) : Event
        data class MarkAsRead(val conversationId: String) : Event
        data class CallUser(val userId: String) : Event
        data class FollowUser(val userId: String) : Event
        data class UnfollowUser(val userId: String) : Event
        data class ViewWhoSeeMe(val userId: String) : Event
    }

    /**
     * 对话数据模型
     */
    data class Conversation(
        val id: String,
        val userId: String,
        val userName: String,
        val userAvatar: String,
        val lastMessage: String,
        val lastMessageTime: Long,
        val unreadCount: Int = 0,
        val isOnline: Boolean = false
    )

    /**
     * 匹配数据模型
     */
    data class Match(
        val id: String,
        val userId: String,
        val userName: String,
        val userAvatar: String,
        val matchTime: Long,
        val hasStartedChat: Boolean = false
    )

    /**
     * 通话记录数据模型
     */
    data class CallRecord(
        val id: String,
        val userId: String,
        val userName: String,
        val userAvatar: String,
        val callType: CallType, // 视频通话或语音通话
        val callTime: Long,
        val duration: Long, // 通话时长（秒）
        val isIncoming: Boolean, // 是否为来电
        val isMissed: Boolean = false // 是否为未接通话
    )

    /**
     * 通话类型枚举
     */
    enum class CallType {
        VOICE, VIDEO
    }

    /**
     * 关注用户数据模型
     */
    data class FollowUser(
        val id: String,
        val userId: String,
        val userName: String,
        val userAvatar: String,
        val bio: String,
        val isOnline: Boolean = false,
        val followTime: Long,
        val isFollowingBack: Boolean = false // 是否互相关注
    )

    /**
     * 谁看了我数据模型
     */
    data class WhoSeeMeUser(
        val id: String,
        val userId: String,
        val userName: String,
        val userAvatar: String,
        val viewTime: Long
    )
}
