package com.mobile.app.facee.utils

import android.app.Activity
import android.app.Application
import android.os.Bundle
import com.mobile.app.facee.trace.LogX
import java.lang.ref.WeakReference

object AppContextHolder : Application.ActivityLifecycleCallbacks {

    private const val TAG = "AppContextHolder"
    private lateinit var app: Application

    private var currentActivityRef: WeakReference<Activity>? = null

    fun init(application: Application) {
        app = application
        application.registerActivityLifecycleCallbacks(this)
    }

    @JvmStatic
    fun getApplication(): Application = app

    fun getCurrentActivity(): Activity? = currentActivityRef?.get()

    override fun onActivityCreated(
        activity: Activity, savedInstanceState: Bundle?
    ) {
        LogX.d(TAG, "current activity created $activity")
    }

    override fun onActivityPaused(activity: Activity) {
        LogX.d(TAG, "current activity paused $activity")
    }

    override fun onActivityResumed(activity: Activity) {
        LogX.d(TAG, "current activity resumed $activity")
        currentActivityRef = WeakReference(activity)
    }

    override fun onActivitySaveInstanceState(
        activity: Activity, outState: Bundle
    ) {
        LogX.d(TAG, "current activity saveInstanceState $activity")
    }

    override fun onActivityStarted(activity: Activity) {
        LogX.d(TAG, "current activity started $activity")
    }

    override fun onActivityStopped(activity: Activity) {
        LogX.d(TAG, "current activity stopped $activity")
    }

    override fun onActivityDestroyed(activity: Activity) {
        LogX.d(TAG, "current activity destroyed $activity")
        currentActivityRef?.get()?.let {
            if (it == activity) {
                currentActivityRef = null
            }
        }
    }

}