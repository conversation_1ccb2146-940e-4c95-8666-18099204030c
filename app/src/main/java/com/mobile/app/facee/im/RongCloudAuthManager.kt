package com.mobile.app.facee.im

import kotlinx.coroutines.suspendCancellableCoroutine
import io.rong.imlib.RongIMClient
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * 融云IM认证管理器
 * 负责处理融云IM的用户认证和连接
 */
object RongCloudAuthManager {

    /**
     * 连接融云IM
     * @param token 从服务器获取的融云Token
     * @return 连接成功返回用户ID
     */
    suspend fun connectToRongCloud(token: String): String = suspendCancellableCoroutine { continuation ->
        RongCloudManager.connect(token, object : RongIMClient.ConnectCallback() {
            override fun onSuccess(userId: String?) {
                if (userId != null) {
                    continuation.resume(userId)
                } else {
                    continuation.resumeWithException(Exception("用户ID为空"))
                }
            }

            override fun onError(errorCode: RongIMClient.ConnectionErrorCode?) {
                val errorMessage = when (errorCode) {
                    RongIMClient.ConnectionErrorCode.RC_CONN_TOKEN_INCORRECT -> "Token错误"
                    RongIMClient.ConnectionErrorCode.RC_CONN_NOT_AUTHRORIZED -> "未授权"
                    RongIMClient.ConnectionErrorCode.RC_CONN_PACKAGE_NAME_INVALID -> "包名无效"
                    RongIMClient.ConnectionErrorCode.RC_CONN_APP_BLOCKED_OR_DELETED -> "应用被封禁或删除"
                    RongIMClient.ConnectionErrorCode.RC_CONN_USER_BLOCKED -> "用户被封禁"
                    RongIMClient.ConnectionErrorCode.RC_DISCONN_KICK -> "用户在其他设备登录"
                    RongIMClient.ConnectionErrorCode.RC_CONN_OTHER_DEVICE_LOGIN -> "其他设备登录"
                    else -> "连接失败: ${errorCode?.name}"
                }
                continuation.resumeWithException(Exception(errorMessage))
            }

            override fun onDatabaseOpened(code: RongIMClient.DatabaseOpenStatus?) {
                // 数据库打开状态，可以在这里处理一些初始化逻辑
            }
        })
    }

    /**
     * 断开融云IM连接
     */
    fun disconnect() {
        RongCloudManager.disconnect()
    }

    /**
     * 获取融云Token（模拟方法，实际应该从服务器获取）
     * 在实际项目中，这个方法应该调用后端API获取Token
     */
    suspend fun getRongCloudToken(userId: String): String {
        // TODO: 实现从服务器获取融云Token的逻辑
        // 这里返回一个模拟的Token，实际使用时需要替换为真实的API调用
        return "mock_token_for_user_$userId"
    }

    /**
     * 检查融云连接状态
     */
    fun isConnected(): Boolean {
        return RongCloudManager.isConnected()
    }

    /**
     * 自动连接融云IM
     * 如果用户已登录，自动获取Token并连接
     */
    suspend fun autoConnect(userId: String): Boolean {
        return try {
            if (!isConnected()) {
                val token = getRongCloudToken(userId)
                connectToRongCloud(token)
                true
            } else {
                true
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
}
