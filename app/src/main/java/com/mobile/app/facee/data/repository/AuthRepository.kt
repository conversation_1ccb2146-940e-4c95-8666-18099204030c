package com.mobile.app.facee.data.repository

import android.util.Log
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.mobile.app.facee.data.model.AuthBean
import com.mobile.app.facee.data.network.ApiResult
import com.mobile.app.facee.data.network.AuthManager
import com.mobile.app.facee.data.network.RequestParamsBuilder
import com.mobile.app.facee.data.service.AuthApi
import com.mobile.app.facee.data.service.SignInRequest
import com.mobile.app.facee.data.service.onError
import com.mobile.app.facee.data.service.onSuccess
import com.mobile.app.facee.trace.LogX
import com.mobile.app.facee.utils.AppContextHolder
import com.mobile.app.facee.utils.UniqueIDUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 认证数据仓库
 * 负责处理登录相关的数据操作
 */
class AuthRepository(
    private val authApi: AuthApi
) {

    private val TAG = "AuthRepository"

    /**
     * Google登录
     */
    suspend fun googleSignIn(account: GoogleSignInAccount): ApiResult<AuthBean?> {
        return withContext(Dispatchers.IO) {
            runCatching {
                LogX.d("开始Google登录: ${account.email}")

                val request = SignInRequest(
                    auth_token = account.idToken ?: "",
                    auth_id = account.email ?: "",
                    auth_type = 4,
                    country_id = 0,
                )
                authApi.signIn(request).onSuccess {
                    AuthManager.saveTokens(accessToken = it.accessToken)
                    RequestParamsBuilder.updateUID(it.user?.id ?: "")
                }.onError { code, message ->
                    LogX.e("Google登录失败: $code - $message")
                }
            }.getOrElse {
                LogX.e(TAG, Log.getStackTraceString(it))
                ApiResult.Error(exception = it)
            }
        }
    }

    /**
     * 游客登录
     */
    suspend fun guestSignIn(): ApiResult<AuthBean?> {
        return withContext(Dispatchers.IO) {
            try {
                val request = SignInRequest(
                    auth_token = UniqueIDUtil.getUniqueID(AppContextHolder.getApplication()),
                    auth_id = UniqueIDUtil.getUniqueID(AppContextHolder.getApplication()),
                    auth_type = 1,
                    country_id = 0,
                )

                authApi.signIn(request).onSuccess {
                    AuthManager.saveTokens(accessToken = it.accessToken)
                    RequestParamsBuilder.updateUID(it.user?.id ?: "")
                }.onError { code, message ->
                    LogX.e("游客登录失败: $code - $message")
                }
            } catch (e: Exception) {
                LogX.e("游客登录异常", e)
                ApiResult.Error(exception = e)
            }
        }
    }

    /**
     * 登出
     */
    suspend fun logout(): ApiResult<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                LogX.d("开始登出")
                authApi.logout()
                // 无论API调用是否成功，都清除本地令牌
                AuthManager.clearTokens()
                LogX.d("本地令牌已清除")
                ApiResult.Success(Unit)

            } catch (e: Exception) {
                LogX.e("登出异常", e)
                // 即使API调用失败，也要清除本地令牌
                AuthManager.clearTokens()
                ApiResult.Error(exception = e)
            }
        }
    }

    /**
     * 检查登录状态
     */
    fun isLoggedIn(): Boolean {
        return AuthManager.isLoggedIn()
    }

    /**
     * 获取当前用户令牌
     */
    fun getCurrentToken(): String? {
        return AuthManager.getAccessToken()
    }
}
