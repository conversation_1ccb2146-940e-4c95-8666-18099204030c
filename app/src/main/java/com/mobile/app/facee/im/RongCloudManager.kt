package com.mobile.app.facee.im

import android.content.Context
import io.rong.imkit.RongIM
import io.rong.imkit.config.RongConfigCenter
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import com.mobile.app.facee.utils.AppContextHolder

/**
 * 融云IM管理器
 * 负责融云IM的初始化、连接和配置
 */
object RongCloudManager {

    // 融云AppKey，需要从融云控制台获取
    private const val RONG_CLOUD_APP_KEY = "your_rong_cloud_app_key"
    
    private var isInitialized = false
    private var isConnected = false

    /**
     * 初始化融云IM
     */
    fun init(context: Context) {
        if (isInitialized) return
        
        try {
            // 初始化融云IM
//            RongIM.init(context, RONG_CLOUD_APP_KEY, false)
            
            // 配置融云IM
            configureRongIM()
            
            isInitialized = true
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 配置融云IM
     */
    private fun configureRongIM() {
        // 设置会话列表界面相关配置
        RongConfigCenter.conversationListConfig().apply {
            // 设置会话列表为聚合模式
//            enableShowUnreadMessageCount(true)
            // 设置显示未读消息数
//            enableShowUnreadMessageCount(true)
        }

        // 设置会话界面相关配置
        RongConfigCenter.conversationConfig().apply {
            // 启用阅读回执
//            enableReadReceipt(true)
            // 启用消息撤回
//            enableMessageRecall(true)
        }
    }

    /**
     * 连接融云IM
     */
    fun connect(token: String, callback: RongIMClient.ConnectCallback?) {
        if (!isInitialized) {
            init(AppContextHolder.getApplication())
        }

        RongIM.connect(token, object : RongIMClient.ConnectCallback() {
            override fun onSuccess(userId: String?) {
                isConnected = true
                callback?.onSuccess(userId)
            }

            override fun onError(errorCode: RongIMClient.ConnectionErrorCode?) {
                isConnected = false
                callback?.onError(errorCode)
            }

            override fun onDatabaseOpened(code: RongIMClient.DatabaseOpenStatus?) {
                callback?.onDatabaseOpened(code)
            }
        })
    }

    /**
     * 断开连接
     */
    fun disconnect() {
        if (isConnected) {
            RongIM.getInstance().disconnect()
            isConnected = false
        }
    }

    /**
     * 获取连接状态
     */
    fun isConnected(): Boolean = isConnected

    /**
     * 获取初始化状态
     */
    fun isInitialized(): Boolean = isInitialized

    /**
     * 获取会话列表
     */
    fun getConversationList(
        conversationTypes: Array<Conversation.ConversationType>,
        callback: RongIMClient.ResultCallback<List<Conversation>>
    ) {
        RongIM.getInstance().getConversationList(callback, *conversationTypes)
    }

    /**
     * 获取历史消息
     */
    fun getHistoryMessages(
        conversationType: Conversation.ConversationType,
        targetId: String,
        oldestMessageId: Int,
        count: Int,
        callback: RongIMClient.ResultCallback<List<Message>>
    ) {
        RongIM.getInstance().getHistoryMessages(
            conversationType,
            targetId,
            oldestMessageId,
            count,
            callback
        )
    }

    /**
     * 发送消息
     */
    fun sendMessage(
        conversationType: Conversation.ConversationType,
        targetId: String,
        content: io.rong.imlib.model.MessageContent,
        pushContent: String? = null,
        pushData: String? = null,
        callback: RongIMClient.SendMessageCallback? = null
    ) {
//        RongIM.getInstance().sendMessage(
//            conversationType,
//            targetId,
//            content,
//            pushContent,
//            pushData,
//            callback
//        )
    }

    /**
     * 清除会话未读消息数
     */
    fun clearMessagesUnreadStatus(
        conversationType: Conversation.ConversationType,
        targetId: String,
        callback: RongIMClient.ResultCallback<Boolean>? = null
    ) {
        RongIM.getInstance().clearMessagesUnreadStatus(conversationType, targetId, callback)
    }

    /**
     * 删除会话
     */
    fun removeConversation(
        conversationType: Conversation.ConversationType,
        targetId: String,
        callback: RongIMClient.ResultCallback<Boolean>? = null
    ) {
//        RongIM.getInstance().removeConversation(conversationType, targetId, callback)
    }
}
