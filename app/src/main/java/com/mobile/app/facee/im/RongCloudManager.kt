package com.mobile.app.facee.im

import android.content.Context
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.mobile.app.facee.R
import com.mobile.app.facee.trace.TraceX
import com.mobile.app.facee.utils.AppContextHolder
import io.rong.imkit.CustomSystemConversationProvider
import io.rong.imkit.GlideKitImageEngine
import io.rong.imkit.IMCenter
import io.rong.imkit.MyCustomConversationProvider
import io.rong.imkit.RongIM
import io.rong.imkit.config.RongConfigCenter
import io.rong.imkit.conversation.extension.MyExtensionConfig
import io.rong.imkit.conversation.extension.RongExtensionManager
import io.rong.imkit.conversation.extension.parsemessage.MikChatAskGiftMessage
import io.rong.imkit.conversation.extension.parsemessage.MikChatGiftMessage
import io.rong.imkit.conversation.extension.parsemessage.MikChatSystemMessage
import io.rong.imkit.conversation.extension.parsemessage.MikChatVideoCallMessage
import io.rong.imkit.conversation.extension.provider.AskGiftMessageItemProvider
import io.rong.imkit.conversation.extension.provider.GiftMessageItemProvider
import io.rong.imkit.conversation.extension.provider.MyImageMessageItemProvider
import io.rong.imkit.conversation.extension.provider.MySightMessageItemProvider
import io.rong.imkit.conversation.extension.provider.MyTextMessageItemProvider
import io.rong.imkit.conversation.extension.provider.SystemNoticeMessageItemProvider
import io.rong.imkit.conversation.extension.provider.VideCallMessageItemProvider
import io.rong.imkit.conversation.messgelist.provider.ImageMessageItemProvider
import io.rong.imkit.conversation.messgelist.provider.SightMessageItemProvider
import io.rong.imkit.conversation.messgelist.provider.TextMessageItemProvider
import io.rong.imkit.conversationlist.provider.PrivateConversationProvider
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.InitOption
import io.rong.imlib.model.Message
import io.rong.message.SightMessage
import java.util.Locale

/**
 * 融云IM管理器
 * 负责融云IM的初始化、连接和配置
 */
object RongCloudManager {

    // 融云AppKey，需要从融云控制台获取
    private const val RONG_CLOUD_APP_KEY = "your_rong_cloud_app_key"

    private var isInitialized = false
    private var isConnected = false

    /**
     * 初始化融云IM
     */
    fun init(context: Context) {
        if (isInitialized) return

        try {
            // 初始化融云IM
//            RongIM.init(context, RONG_CLOUD_APP_KEY, false)

            // 配置融云IM
            configureRongIM()

            isInitialized = true
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun setUpRongYunIM(rongCloudAppKey: String) {
        val areaCode = InitOption.AreaCode.SG
        TraceX.e("setUpRongYunIM $rongCloudAppKey  $areaCode")

        // 初始化 RongIM
        initRongIMClient(rongCloudAppKey, areaCode)
        // 设置 Glide 图片加载引擎
        setGlideImageEngine()
        // 注册自定义模板和消息提供者
        registerCustomConversationProvider()
        // 配置扩展和消息
        configureRongIMFeatures()
        // 配置语言
        configureLanguage()
        // 注册自定义消息类型
        registerCustomMessageTypes()
    }

    // 初始化 RongIM 客户端
    private fun initRongIMClient(appKey: String, areaCode: InitOption.AreaCode) {
        val initOption = InitOption.Builder().setAreaCode(areaCode).build()
        IMCenter.init(AppContextHolder.getApplication(), appKey, initOption)
    }

    // 设置 Glide 图片加载引擎
    private fun setGlideImageEngine() {
        RongConfigCenter.featureConfig().kitImageEngine = object : GlideKitImageEngine() {
            private val defaultPlaceholder = R.mipmap.icon_default_avatar

            override fun loadConversationListPortrait(
                context: Context, url: String, imageView: ImageView, conversation: Conversation
            ) {
                configImage(context, url, imageView)
            }

            override fun loadConversationPortrait(
                context: Context, url: String, imageView: ImageView, message: Message?
            ) {
                configImage(context, url, imageView)
            }

            private fun configImage(context: Context, url: String, imageView: ImageView) {
                Glide.with(context)
                    .load(url)
                    .apply(RequestOptions.bitmapTransform(CircleCrop()))
                    .placeholder(defaultPlaceholder)
                    .error(defaultPlaceholder)
                    .into(imageView)
            }
        }
    }

    // 注册自定义会话模板和消息提供者
    private fun registerCustomConversationProvider() {
        val providerManager = RongConfigCenter.conversationListConfig().providerManager
        providerManager.replaceProvider(
            PrivateConversationProvider::class.java,
            MyCustomConversationProvider()
        )
        providerManager.addProvider(CustomSystemConversationProvider())

        // 注册自定义会话页面
//        RouteUtils.registerActivity(
//            RouteUtils.RongActivityType.ConversationActivity, MyRongConversationActivity::class.java
//        )
    }

    // 配置 RongIM 扩展和消息
    private fun configureRongIMFeatures() {
        RongExtensionManager.getInstance().extensionConfig = MyExtensionConfig()

        RongConfigCenter.conversationConfig().apply {
            isShowMoreClickAction = false
            conversationHistoryMessageCount = 100
            conversationRemoteMessageCount = 100
            addMessageProvider(GiftMessageItemProvider())
            addMessageProvider(AskGiftMessageItemProvider())
            addMessageProvider(VideCallMessageItemProvider())
            addMessageProvider(SystemNoticeMessageItemProvider())
            replaceMessageProvider(TextMessageItemProvider::class.java, MyTextMessageItemProvider())
            replaceMessageProvider(
                ImageMessageItemProvider::class.java,
                MyImageMessageItemProvider()
            )
            replaceMessageProvider(
                SightMessageItemProvider::class.java,
                MySightMessageItemProvider()
            )
        }
    }

    // 配置语言设置
    private fun configureLanguage() {
        var localLanguage = Locale.getDefault().language
        if (localLanguage == "zh") {
            val country = Locale.getDefault().country
            localLanguage = when (country) {
                "TW", "HK", "MO" -> "zh_TW" // 繁体中文
                else -> "zh_CN" // 简体中文
            }
        }
        RongConfigCenter.featureConfig().rc_translation_target_language = localLanguage
    }

    // 注册自定义消息类型
    private fun registerCustomMessageTypes() {
        val myMessages = arrayListOf(
            MikChatGiftMessage::class.java,
            MikChatAskGiftMessage::class.java,
            MikChatVideoCallMessage::class.java,
            MikChatSystemMessage::class.java,
            SightMessage::class.java
        )
        RongIMClient.registerMessageType(myMessages)
    }


    /**
     * 配置融云IM
     */
    private fun configureRongIM() {
        // 设置会话列表界面相关配置
        RongConfigCenter.conversationListConfig().apply {
            // 设置会话列表为聚合模式
//            enableShowUnreadMessageCount(true)
            // 设置显示未读消息数
//            enableShowUnreadMessageCount(true)
        }

        // 设置会话界面相关配置
        RongConfigCenter.conversationConfig().apply {
            // 启用阅读回执
//            enableReadReceipt(true)
            // 启用消息撤回
//            enableMessageRecall(true)
        }
    }

    /**
     * 连接融云IM
     */
    fun connect(token: String, callback: RongIMClient.ConnectCallback?) {
        if (!isInitialized) {
            init(AppContextHolder.getApplication())
        }

        RongIM.connect(token, object : RongIMClient.ConnectCallback() {
            override fun onSuccess(userId: String?) {
                isConnected = true
                callback?.onSuccess(userId)
            }

            override fun onError(errorCode: RongIMClient.ConnectionErrorCode?) {
                isConnected = false
                callback?.onError(errorCode)
            }

            override fun onDatabaseOpened(code: RongIMClient.DatabaseOpenStatus?) {
                callback?.onDatabaseOpened(code)
            }
        })
    }

    /**
     * 断开连接
     */
    fun disconnect() {
        if (isConnected) {
            RongIM.getInstance().disconnect()
            isConnected = false
        }
    }

    /**
     * 获取连接状态
     */
    fun isConnected(): Boolean = isConnected

    /**
     * 获取初始化状态
     */
    fun isInitialized(): Boolean = isInitialized

    /**
     * 获取会话列表
     */
    fun getConversationList(
        conversationTypes: Array<Conversation.ConversationType>,
        callback: RongIMClient.ResultCallback<List<Conversation>>
    ) {
        RongIM.getInstance().getConversationList(callback, *conversationTypes)
    }

    /**
     * 获取历史消息
     */
    fun getHistoryMessages(
        conversationType: Conversation.ConversationType,
        targetId: String,
        oldestMessageId: Int,
        count: Int,
        callback: RongIMClient.ResultCallback<List<Message>>
    ) {
        RongIM.getInstance().getHistoryMessages(
            conversationType,
            targetId,
            oldestMessageId,
            count,
            callback
        )
    }

    /**
     * 发送消息
     */
    fun sendMessage(
        conversationType: Conversation.ConversationType,
        targetId: String,
        content: io.rong.imlib.model.MessageContent,
        pushContent: String? = null,
        pushData: String? = null,
        callback: RongIMClient.SendMessageCallback? = null
    ) {
//        RongIM.getInstance().sendMessage(
//            conversationType,
//            targetId,
//            content,
//            pushContent,
//            pushData,
//            callback
//        )
    }

    /**
     * 清除会话未读消息数
     */
    fun clearMessagesUnreadStatus(
        conversationType: Conversation.ConversationType,
        targetId: String,
        callback: RongIMClient.ResultCallback<Boolean>? = null
    ) {
        RongIM.getInstance().clearMessagesUnreadStatus(conversationType, targetId, callback)
    }

    /**
     * 删除会话
     */
    fun removeConversation(
        conversationType: Conversation.ConversationType,
        targetId: String,
        callback: RongIMClient.ResultCallback<Boolean>? = null
    ) {
//        RongIM.getInstance().removeConversation(conversationType, targetId, callback)
    }
}
