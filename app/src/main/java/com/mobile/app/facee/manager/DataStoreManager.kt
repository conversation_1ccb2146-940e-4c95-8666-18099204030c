package com.mobile.app.facee.manager

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.floatPreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.core.stringSetPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.mobile.app.facee.data.model.UserBean
import com.mobile.app.facee.trace.LogX
import com.mobile.app.facee.utils.AppContextHolder
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.runBlocking
import java.util.Locale

/**
 * DataStore 管理器
 * 提供便捷的数据存储和读取方法，基于 DataStore 实现
 * 使用 ActivityUtils 获取 Context
 */
object DataStoreManager {
    private const val DATASTORE_NAME = "facee_preferences"

    // 常用的 Key 常量
    const val KEY_USER_ID = "user_id"
    const val KEY_ACCESS_TOKEN = "access_token"
    const val KEY_REFRESH_TOKEN = "refresh_token"
    const val KEY_IS_FIRST_LAUNCH = "is_first_launch"
    const val KEY_THEME_MODE = "theme_mode"
    const val KEY_LANGUAGE = "app_language"
    const val KEY_NOTIFICATION_ENABLED = "notification_enabled"
    const val KEY_GLOBAL_NOTIFICATION_ENABLED = "global_notification_enabled"
    const val KEY_RONG_YUN_APP_KEY = "rong_yun_app_key"
    const val KEY_RONG_YUN_TOKEN = "rong_yun_token"
    const val KEY_PROFILE_COMPLETED = "profile_completed"
    const val KEY_USER_OBJECT = "user_object"  // 完整用户对象
    const val KEY_ANCHOR_CONFIG = "anchor_config"  // 登录配置对象
    const val KEY_REVIEW_INFO = "review_info"
    const val KEY_CURRENCY = "currency"  // 货币设置
    const val KEY_APP_ID = "app_id"  //

    // DataStore Keys
    private val USER_ID_KEY = stringPreferencesKey(KEY_USER_ID)
    private val ACCESS_TOKEN_KEY = stringPreferencesKey(KEY_ACCESS_TOKEN)
    private val REFRESH_TOKEN_KEY = stringPreferencesKey(KEY_REFRESH_TOKEN)
    private val IS_FIRST_LAUNCH_KEY = booleanPreferencesKey(KEY_IS_FIRST_LAUNCH)
    private val THEME_MODE_KEY = stringPreferencesKey(KEY_THEME_MODE)
    private val LANGUAGE_KEY = stringPreferencesKey(KEY_LANGUAGE)
    private val NOTIFICATION_ENABLED_KEY = booleanPreferencesKey(KEY_NOTIFICATION_ENABLED)
    private val GLOBAL_NOTIFICATION_ENABLED_KEY =
        booleanPreferencesKey(KEY_GLOBAL_NOTIFICATION_ENABLED)

    // DataStore 实例
    private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = DATASTORE_NAME)
    private val dataStore: DataStore<Preferences>
        get() = AppContextHolder.getApplication().dataStore

    // JSON 序列化工具
    private val moshi = Moshi.Builder().add(KotlinJsonAdapterFactory()).build()
    private val userBeanAdapter = moshi.adapter(UserBean::class.java)

    /**
     * 存储字符串
     */
    suspend fun putString(key: String, value: String?) {
        val prefKey = stringPreferencesKey(key)
        dataStore.edit { preferences ->
            if (value != null) {
                preferences[prefKey] = value
            } else {
                preferences.remove(prefKey)
            }
        }
    }

    /**
     * 获取字符串
     */
    suspend fun getString(key: String, defaultValue: String? = null): String? {
        val prefKey = stringPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }.first()
    }

    fun getStringSync(key: String, defaultValue: String? = null): String? {
        return runBlocking {
            val prefKey = stringPreferencesKey(key)
            dataStore.data.map { preferences ->
                preferences[prefKey] ?: defaultValue
            }.first()
        }
    }

    /**
     * 获取字符串 Flow
     */
    fun getStringFlow(key: String, defaultValue: String? = null): Flow<String?> {
        val prefKey = stringPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }
    }

    /**
     * 存储整数
     */
    suspend fun putInt(key: String, value: Int) {
        val prefKey = intPreferencesKey(key)
        dataStore.edit { preferences ->
            preferences[prefKey] = value
        }
    }

    /**
     * 获取整数
     */
    suspend fun getInt(key: String, defaultValue: Int = 0): Int {
        val prefKey = intPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }.first()
    }

    /**
     * 获取整数 Flow
     */
    fun getIntFlow(key: String, defaultValue: Int = 0): Flow<Int> {
        val prefKey = intPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }
    }

    /**
     * 存储长整数
     */
    suspend fun putLong(key: String, value: Long) {
        val prefKey = longPreferencesKey(key)
        dataStore.edit { preferences ->
            preferences[prefKey] = value
        }
    }

    /**
     * 获取长整数
     */
    suspend fun getLong(key: String, defaultValue: Long = 0L): Long {
        val prefKey = longPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }.first()
    }

    /**
     * 获取长整数 Flow
     */
    fun getLongFlow(key: String, defaultValue: Long = 0L): Flow<Long> {
        val prefKey = longPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }
    }

    /**
     * 存储浮点数
     */
    suspend fun putFloat(key: String, value: Float) {
        val prefKey = floatPreferencesKey(key)
        dataStore.edit { preferences ->
            preferences[prefKey] = value
        }
    }

    /**
     * 获取浮点数
     */
    suspend fun getFloat(key: String, defaultValue: Float = 0f): Float {
        val prefKey = floatPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }.first()
    }

    /**
     * 获取浮点数 Flow
     */
    fun getFloatFlow(key: String, defaultValue: Float = 0f): Flow<Float> {
        val prefKey = floatPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }
    }

    /**
     * 存储布尔值
     */
    suspend fun putBoolean(key: String, value: Boolean) {
        val prefKey = booleanPreferencesKey(key)
        dataStore.edit { preferences ->
            preferences[prefKey] = value
        }
    }

    /**
     * 获取布尔值
     */
    suspend fun getBoolean(key: String, defaultValue: Boolean = false): Boolean {
        val prefKey = booleanPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }.first()
    }

    /**
     * 获取布尔值 Flow
     */
    fun getBooleanFlow(key: String, defaultValue: Boolean = false): Flow<Boolean> {
        val prefKey = booleanPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }
    }

    /**
     * 存储字符串集合
     */
    suspend fun putStringSet(key: String, value: Set<String>?) {
        val prefKey = stringSetPreferencesKey(key)
        dataStore.edit { preferences ->
            if (value != null) {
                preferences[prefKey] = value
            } else {
                preferences.remove(prefKey)
            }
        }
    }

    /**
     * 获取字符串集合
     */
    suspend fun getStringSet(key: String, defaultValue: Set<String>? = null): Set<String>? {
        val prefKey = stringSetPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }.first()
    }

    /**
     * 获取字符串集合 Flow
     */
    fun getStringSetFlow(key: String, defaultValue: Set<String>? = null): Flow<Set<String>?> {
        val prefKey = stringSetPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }
    }

    /**
     * 移除指定键的数据
     */
    suspend fun remove(key: String) {
        val stringKey = stringPreferencesKey(key)
        val intKey = intPreferencesKey(key)
        val longKey = longPreferencesKey(key)
        val floatKey = floatPreferencesKey(key)
        val booleanKey = booleanPreferencesKey(key)
        val stringSetKey = stringSetPreferencesKey(key)

        dataStore.edit { preferences ->
            preferences.remove(stringKey)
            preferences.remove(intKey)
            preferences.remove(longKey)
            preferences.remove(floatKey)
            preferences.remove(booleanKey)
            preferences.remove(stringSetKey)
        }
    }

    /**
     * 清除所有数据
     */
    suspend fun clear() {
        dataStore.edit { preferences ->
            preferences.clear()
        }
    }

    /**
     * 检查是否包含指定键
     */
    suspend fun contains(key: String): Boolean {
        val stringKey = stringPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences.contains(stringKey)
        }.first()
    }

    /**
     * 获取所有键值对的 Flow
     */
    fun getAllFlow(): Flow<Map<String, Any?>> {
        return dataStore.data.map { preferences ->
            preferences.asMap().mapKeys { it.key.name }
        }
    }

    /**
     * 获取用户 ID
     */
    fun getUserId(): String? = runBlocking { getString(KEY_USER_ID) }

    /**
     * 获取用户 ID（同步方式，用于兼容）
     */
    fun getUserIdSync(): String? = runBlocking { getUserId() }

    /**
     * 获取用户 ID Flow
     */
    fun getUserIdFlow(): Flow<String?> = getStringFlow(KEY_USER_ID)

    /**
     * 保存访问令牌
     */
    suspend fun saveTokens(accessToken: String, refreshToken: String? = null) {
        dataStore.edit { preferences ->
            preferences[ACCESS_TOKEN_KEY] = accessToken
            refreshToken?.let { preferences[REFRESH_TOKEN_KEY] = it }
        }
    }

    /**
     * 获取访问令牌
     */
    suspend fun getAccessToken(): String? = getString(KEY_ACCESS_TOKEN)

    /**
     * 获取访问令牌（同步方式，用于兼容）
     */
    fun getAccessTokenSync(): String? = runBlocking { getAccessToken() }

    /**
     * 获取访问令牌 Flow
     */
    fun getAccessTokenFlow(): Flow<String?> = getStringFlow(KEY_ACCESS_TOKEN)

    /**
     * 获取刷新令牌
     */
    suspend fun getRefreshToken(): String? = getString(KEY_REFRESH_TOKEN)

    /**
     * 获取刷新令牌（同步方式，用于兼容）
     */
    fun getRefreshTokenSync(): String? = runBlocking { getRefreshToken() }

    /**
     * 获取刷新令牌 Flow
     */
    fun getRefreshTokenFlow(): Flow<String?> = getStringFlow(KEY_REFRESH_TOKEN)

    /**
     * 清除用户数据
     */
    suspend fun clearUserData() {
        dataStore.edit { preferences ->
            preferences.remove(USER_ID_KEY)
            preferences.remove(ACCESS_TOKEN_KEY)
            preferences.remove(REFRESH_TOKEN_KEY)
        }
    }

    /**
     * 是否首次启动
     */
    suspend fun isFirstLaunch(): Boolean = getBoolean(KEY_IS_FIRST_LAUNCH, true)

    /**
     * 是否首次启动（同步方式，用于兼容）
     */
    fun isFirstLaunchSync(): Boolean = runBlocking { isFirstLaunch() }

    /**
     * 是否首次启动 Flow
     */
    fun isFirstLaunchFlow(): Flow<Boolean> = getBooleanFlow(KEY_IS_FIRST_LAUNCH, true)

    /**
     * 设置已启动过
     */
    suspend fun setNotFirstLaunch() {
        putBoolean(KEY_IS_FIRST_LAUNCH, false)
    }

    /**
     * 检查用户资料是否已完善
     */
    suspend fun isProfileCompleted(): Boolean = getBoolean(KEY_PROFILE_COMPLETED, false)

    /**
     * 检查用户资料是否已完善（同步方式，用于兼容）
     */
    fun isProfileCompletedSync(): Boolean = runBlocking { isProfileCompleted() }

    /**
     * 检查用户资料是否已完善 Flow
     */
    fun isProfileCompletedFlow(): Flow<Boolean> = getBooleanFlow(KEY_PROFILE_COMPLETED, false)

    /**
     * 设置用户资料已完善
     */
    suspend fun setProfileCompleted(completed: Boolean = true) {
        putBoolean(KEY_PROFILE_COMPLETED, completed)
    }

    // ==================== 用户对象存储方法 ====================

    /**
     * 保存完整的用户对象
     * 登录成功后调用此方法保存整个用户对象
     *
     * @param userBean 用户对象
     */
    suspend fun saveUserObject(userBean: UserBean?) {
        try {
            if (userBean == null) {
                // 如果传入null，则清除用户对象
                putString(KEY_USER_OBJECT, null)
                LogX.d("DataStoreManager: 清除用户对象")
                return
            }

            // 将用户对象序列化为JSON字符串
            val userJson = userBeanAdapter.toJson(userBean)
            putString(KEY_USER_OBJECT, userJson)

            // 保存融云相关信息
//            putString(KEY_RONG_YUN_TOKEN, userBean.rongcloudToken)
//            putString(KEY_RONG_YUN_APP_KEY, userBean.rongcloudAppID)
//            RongYunUtil.refreshCacheUserInfo(userBean)
        } catch (e: Exception) {
            LogX.e("DataStoreManager: 保存用户对象失败", e)
        }
    }

    /**
     * 获取完整的用户对象（同步方式，用于兼容）
     *
     * @return 用户对象，如果不存在或解析失败则返回null
     */
    fun getUserObject(): UserBean? = runBlocking {
        try {
            val userJson = getString(KEY_USER_OBJECT)
            if (userJson.isNullOrBlank()) {
                LogX.d("DataStoreManager: 用户对象不存在")
                return@runBlocking null
            }

            val userBean = userBeanAdapter.fromJson(userJson)
            userBean
        } catch (e: Exception) {
            LogX.e("DataStoreManager: 获取用户对象失败", e)
            null
        }
    }

    /**
     * 获取用户对象 Flow
     *
     * @return 用户对象的Flow
     */
    fun getUserObjectFlow(): Flow<UserBean?> {
        return getStringFlow(KEY_USER_OBJECT).map { userJson ->
            try {
                if (userJson.isNullOrBlank()) {
                    null
                } else {
                    userBeanAdapter.fromJson(userJson)
                }
            } catch (e: Exception) {
                LogX.e("DataStoreManager: 解析用户对象失败", e)
                null
            }
        }
    }

    /**
     * 更新用户对象的部分字段
     *
     * @param updateAction 更新操作，接收当前用户对象并返回更新后的对象
     */
    suspend fun updateUserObject(updateAction: (UserBean?) -> UserBean?) {
        try {
            val currentUser = getUserObject()
            val updatedUser = updateAction(currentUser)
            saveUserObject(updatedUser)
            LogX.d("DataStoreManager: 用户对象更新完成")
        } catch (e: Exception) {
            LogX.e("DataStoreManager: 更新用户对象失败", e)
        }
    }

    /**
     * 清除用户数据（增强版）
     * 清除所有用户相关数据，包括用户对象和登录配置
     */
    suspend fun clearAllUserData() {
        dataStore.edit { preferences ->
            // 清除基本用户信息
            preferences.remove(USER_ID_KEY)
            preferences.remove(ACCESS_TOKEN_KEY)
            preferences.remove(REFRESH_TOKEN_KEY)

            // 清除用户对象
            preferences.remove(stringPreferencesKey(KEY_USER_OBJECT))

            // 清除登录配置对象
            preferences.remove(stringPreferencesKey(KEY_ANCHOR_CONFIG))

            // 清除融云相关
            preferences.remove(stringPreferencesKey(KEY_RONG_YUN_TOKEN))
            preferences.remove(stringPreferencesKey(KEY_RONG_YUN_APP_KEY))

            // 清除配置相关字段
            preferences.remove(intPreferencesKey("daily_duration"))
            preferences.remove(stringPreferencesKey("cdn_url"))
            preferences.remove(stringPreferencesKey("agora_app_id"))

            // 重置资料完善状态
            preferences.remove(booleanPreferencesKey(KEY_PROFILE_COMPLETED))
        }
        LogX.d("DataStoreManager: 所有用户数据已清除")
    }

    /**
     * 检查用户是否已登录
     * 基于用户对象和访问令牌的存在性判断
     *
     * @return true表示已登录，false表示未登录
     */
    suspend fun isUserLoggedIn(): Boolean {
        val userObject = getUserObject()
        val accessToken = getAccessToken()
        val isLoggedIn = userObject != null && !accessToken.isNullOrBlank()
        LogX.d("DataStoreManager: 用户登录状态检查 - $isLoggedIn")
        return isLoggedIn
    }

    /**
     * 检查用户是否已登录（同步方式）
     */
    fun isUserLoggedInSync(): Boolean = runBlocking { isUserLoggedIn() }

    // ==================== 登录配置对象存储方法 ====================


    suspend fun saveCurrentLanguage(language: String) {
        putString(KEY_LANGUAGE, language)
    }

    fun getCurrentLanguage(): String =
        runBlocking { getString(KEY_LANGUAGE) } ?: Locale.getDefault().language

    suspend fun saveAppId(appId: String) {
        putString(KEY_APP_ID, appId)
    }

    fun getAppId(): String = runBlocking { getString(KEY_APP_ID) } ?: ""


    // ==================== 登录成功统一处理方法 ====================

    /**
     * 登录成功后的统一数据保存方法
     * 保存用户对象、访问令牌、登录配置等所有登录相关数据
     *
     * @param authBean 登录响应对象
     */
    suspend fun saveLoginData(authBean: com.mobile.app.facee.data.model.AuthBean) {
        try {
            // 保存访问令牌
            saveTokens(authBean.accessToken)

            // 保存用户对象
//            authBean.anchor?.let { userBean ->
//                saveUserObject(userBean)
//                // 同时保存用户ID到单独的key中
//                putString(KEY_USER_ID, userBean.id)
//
//                saveAppId(userBean.appId.toString())
//            }
//
//            // 保存登录配置
//            authBean.config?.let { config ->
//                saveAnchorConfigBean(config)
//            }

            LogX.d("DataStoreManager: 登录数据保存完成")
        } catch (e: Exception) {
            LogX.e("DataStoreManager: 保存登录数据失败", e)
        }
    }


//    suspend fun saveVideoConfig(it: MutableList<RecordTimeBean>) {
//        putString("record_time_config", it.toJson())
//    }
//
//    fun getVideoConfig(): MutableList<RecordTimeBean>? {
//        return runBlocking {
//            val configJson = getString("record_time_config")
//            if (configJson.isNullOrBlank()) {
//                LogX.d("DataStoreManager: videoConfig不存在")
//                return@runBlocking null
//            }
//            val config = MoshiUtil.listFromJson<RecordTimeBean>(configJson)
//            config
//        }
//    }

}
