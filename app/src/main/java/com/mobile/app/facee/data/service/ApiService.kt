package com.mobile.app.facee.data.service

import com.mobile.app.facee.data.network.ApiResult
import com.mobile.app.facee.trace.LogX
import kotlinx.serialization.Serializable

/**
 * API 服务接口
 * 定义所有网络请求的接口
 * 注意：通用参数（appID, cursor, deviceID等）会通过拦截器自动添加
 */
interface ApiService : CommonApiService {

}

@Serializable
data class ApiResponse<T>(
    val code: Int, val message: String, val data: T?
) {

    val isSuccess: Boolean
        get() = code == 0
}

inline fun <T> ApiResponse<T>.onSuccess(
    crossinline action: (T) -> Unit
): ApiResponse<T> {
    if (isSuccess && data != null) {
        action(data)
    }
    return this
}

inline fun <T> ApiResponse<T>.onError(
    crossinline action: (code: Int, message: String) -> Unit
): ApiResult<T?> {
    return if (!isSuccess) {
        LogX.e("API请求失败: $code - $message")
        action(code, message)
        ApiResult.Error(code, message = message)
    } else {
        ApiResult.Success(data)
    }
}