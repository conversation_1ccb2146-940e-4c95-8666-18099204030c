package com.mobile.app.facee.ui.screens.match

import com.slack.circuit.runtime.CircuitUiEvent
import com.slack.circuit.runtime.CircuitUiState

/**
 * 匹配屏幕 - 滑动匹配功能
 */
object MatchScreen {

    /**
     * 匹配页面状态
     */
    data class State(
        val isLoading: Boolean = false,
        val currentUser: MatchUser? = null,
        val userQueue: List<MatchUser> = emptyList(),
        val hasMoreUsers: Boolean = true,
        val eventSink: (Event) -> Unit
    ) : CircuitUiState

    /**
     * 匹配页面事件
     */
    sealed interface Event : CircuitUiEvent {
        data object LoadNextUser : Event
        data class SwipeLeft(val userId: String) : Event
        data class SwipeRight(val userId: String) : Event
        data class SuperLike(val userId: String) : Event
        data object Refresh : Event
        data class ViewProfile(val userId: String) : Event
    }

    /**
     * 匹配用户数据模型
     */
    data class MatchUser(
        val id: String,
        val name: String,
        val age: Int,
        val location: String,
        val distance: Int, // 距离（公里）
        val bio: String,
        val photos: List<String>,
        val interests: List<String> = emptyList(),
        val occupation: String = "",
        val education: String = ""
    )

    /**
     * 匹配结果
     */
    data class MatchResult(
        val isMatch: Boolean,
        val user: MatchUser
    )
}
