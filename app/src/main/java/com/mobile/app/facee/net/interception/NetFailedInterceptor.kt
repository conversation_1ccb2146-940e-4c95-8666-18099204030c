package com.mobile.app.facee.net.interception

import com.facee.netlibrary.interception.NetWorkFailedInterceptor
import com.mobile.app.facee.net.error.code

/**
 *
 * @Author： Lxf
 * @Time： 2022/6/1 2:25 下午
 * @description：网络错误拦截器 返回false表示有自己的处理 不弹toast提示 默认返回true表示使用默认的异常处理
 */
class NetFailedInterceptor : NetWorkFailedInterceptor {
    override fun intercept(throwable: Throwable): <PERSON><PERSON>an {
        when (throwable.code) {
//            Constants.ErrorCode.USER_NOT_EXIST, Constants.ErrorCode.TOKEN_EXPIRED, Constants.ErrorCode.USER_BLOCK -> {
//                ActivityManager.current?.let {
//                    it as AppCompatActivity
//                }?.let {
//                    it.lifecycleScope.launch {
//                        val msg = if (throwable.code == Constants.ErrorCode.USER_BLOCK) {
//                            runCatching {
//                                JSONObject(throwable.message ?: "{}").let {
//                                    it.optString("reason").plus("\n").plus(
//                                        ContextHolder.context?.getString(
//                                            R.string.unblocking_time
//                                        )
//                                    )
//                                        .plus(it.optLong("end_at").formatDateTime())
//                                }
//                            }.getOrElse { null }
//                        } else {
//                            null
//                        }
//
//                        FlowBus.with<String?>(Constants.TOKEN_EXPIRED)
//                            .post(msg)
//                    }
//                }
//                return false
//            }

//            HTTP_BAD_REQUEST, HTTP_INTERNAL_ERROR -> {
////                ActivityManager.current?.let {
////                    it as? AppCompatActivity
////                }?.let {
////                    it.runOnUiThread {
////                        ToastUtil.show(throwable.message)
////                    }
////                }
//                return false
//            }
        }
        return true
    }
}