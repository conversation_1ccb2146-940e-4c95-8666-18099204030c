package com.mobile.app.facee.ui.screens.chat

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import com.mobile.app.facee.ui.screens.AppScreen
import com.slack.circuit.runtime.Navigator
import com.slack.circuit.runtime.presenter.Presenter

/**
 * 聊天页面Presenter
 */
class ChatPresenter(
    private val navigator: Navigator,
    private val userId: String,
    private val userName: String
) : Presenter<ChatScreen.State> {

    @Composable
    override fun present(): ChatScreen.State {
        var isLoading by remember { mutableStateOf(false) }
        var messages by remember { mutableStateOf(getSampleMessages()) }
        var inputText by remember { mutableStateOf("") }
        var isTyping by remember { mutableStateOf(false) }

        return ChatScreen.State(
            isLoading = isLoading,
            userId = userId,
            userName = userName,
            messages = messages,
            inputText = inputText,
            isTyping = isTyping,
            eventSink = { event ->
                when (event) {
                    is ChatScreen.Event.LoadMessages -> {
                        isLoading = true
                        // TODO: 加载聊天记录
                        messages = getSampleMessages()
                        isLoading = false
                    }
                    is ChatScreen.Event.SendMessage -> {
                        if (event.text.isNotBlank()) {
                            val newMessage = ChatScreen.ChatMessage(
                                id = "msg_${System.currentTimeMillis()}",
                                senderId = "current_user",
                                text = event.text,
                                timestamp = System.currentTimeMillis(),
                                isFromMe = true
                            )
                            messages = messages + newMessage
                            inputText = ""
                            
                            // 模拟对方回复
                            simulateReply(event.text) { reply ->
                                messages = messages + reply
                            }
                        }
                    }
                    is ChatScreen.Event.UpdateInputText -> {
                        inputText = event.text
                    }
                    is ChatScreen.Event.Back -> {
                        navigator.pop()
                    }
                    is ChatScreen.Event.ViewProfile -> {
                        navigator.goTo(AppScreen.UserDetailScreen(userId))
                    }
                }
            }
        )
    }

    private fun getSampleMessages(): List<ChatScreen.ChatMessage> {
        return listOf(
            ChatScreen.ChatMessage(
                id = "1",
                senderId = userId,
                text = "你好！很高兴和你匹配成功 😊",
                timestamp = System.currentTimeMillis() - 3600000,
                isFromMe = false
            ),
            ChatScreen.ChatMessage(
                id = "2",
                senderId = "current_user",
                text = "你好！我也很开心认识你",
                timestamp = System.currentTimeMillis() - 3500000,
                isFromMe = true
            ),
            ChatScreen.ChatMessage(
                id = "3",
                senderId = userId,
                text = "你的照片很好看，平时喜欢做什么呢？",
                timestamp = System.currentTimeMillis() - 3000000,
                isFromMe = false
            )
        )
    }

    private fun simulateReply(userMessage: String, onReply: (ChatScreen.ChatMessage) -> Unit) {
        // 简单的自动回复逻辑
        val replies = listOf(
            "哈哈，有趣！",
            "是的，我也这么觉得",
            "真的吗？太棒了！",
            "听起来不错呢",
            "我们有很多共同点呢"
        )
        
        // 延迟1-3秒回复
        val delay = (1000..3000).random().toLong()
        
        // 这里应该用协程，但为了简化示例，我们直接添加回复
        val reply = ChatScreen.ChatMessage(
            id = "reply_${System.currentTimeMillis()}",
            senderId = userId,
            text = replies.random(),
            timestamp = System.currentTimeMillis() + delay,
            isFromMe = false
        )
        
        // 在实际应用中，这里应该使用协程延迟
        onReply(reply)
    }
}
