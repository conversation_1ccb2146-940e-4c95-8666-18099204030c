package com.mobile.app.facee.ui

import android.content.Intent
import android.os.Build
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.splashscreen.SplashScreen
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.lifecycle.lifecycleScope
import com.mobile.app.facee.MainActivity
import com.mobile.app.facee.R
import com.mobile.app.facee.data.network.AuthManager
import com.mobile.app.facee.trace.LogX
import kotlinx.coroutines.launch

class SplashActivity : AppCompatActivity() {

    private var splashScreen: SplashScreen? = null
    private var keepSplashScreen = true

    override fun onCreate(savedInstanceState: Bundle?) {
        // 安装启动屏幕
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            splashScreen = installSplashScreen()
            splashScreen?.setKeepOnScreenCondition { keepSplashScreen }
        }
        super.onCreate(savedInstanceState)

        // 检查登录状态并导航
        checkLoginStatusAndNavigate()
    }

    /**
     * 检查登录状态并导航到相应页面
     */
    private fun checkLoginStatusAndNavigate() {
        lifecycleScope.launch {
            try {
                LogX.d("SplashActivity", "开始检查登录状态...")

                // 检查用户登录状态
                val isLoggedIn = AuthManager.isLoggedIn()
                LogX.d("SplashActivity", "用户登录状态: $isLoggedIn")

                // 根据登录状态导航
                navigateToMainActivity(isLoggedIn)

            } catch (e: Exception) {
                LogX.e("SplashActivity", e)
                LogX.e("SplashActivity", "检查登录状态时发生错误")
                // 发生错误时，默认进入登录页面
                navigateToMainActivity(false)
            }
        }
    }

    /**
     * 导航到MainActivity
     * @param isLoggedIn 是否已登录，MainActivity会根据此状态决定显示登录页面还是主页面
     */
    private fun navigateToMainActivity(isLoggedIn: Boolean) {
        LogX.d("SplashActivity", "导航到MainActivity，登录状态: $isLoggedIn")

        val intent = Intent(this, MainActivity::class.java).apply {
            // 传递登录状态给MainActivity（可选，MainActivity也会自己检查）
            putExtra("is_logged_in", isLoggedIn)
        }

        startActivity(intent)

        // 关闭启动屏幕
        keepSplashScreen = false
        finish()
    }

    override fun finish() {
        super.finish()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            overrideActivityTransition(OVERRIDE_TRANSITION_CLOSE, 0, R.anim.fade_out)
        }
    }
}