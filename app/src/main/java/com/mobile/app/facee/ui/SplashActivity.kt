package com.mobile.app.facee.ui

import android.content.Intent
import android.os.Build
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.splashscreen.SplashScreen
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import com.mobile.app.facee.MainActivity

class SplashActivity : AppCompatActivity() {

    var splashScreen: SplashScreen? = null
    private var keepSplashScreen = true

    override fun onCreate(savedInstanceState: Bundle?) {

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            splashScreen = installSplashScreen()
            splashScreen?.setKeepOnScreenCondition { keepSplashScreen }
        }
        super.onCreate(savedInstanceState)

        gotoActivity()
    }

    private fun gotoActivity() {
//            if (AuthManager.isLoggedIn()) {
//                jumpThenFinish(MainActivity::class.java)
//            } else {
//                jumpThenFinish(LoginActivity::class.java)
//            }
        startActivity(Intent(this, MainActivity::class.java))
        finish()
    }
}