package com.mobile.app.facee.i18n

import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.AppCompatEditText
import androidx.appcompat.widget.AppCompatTextView
import com.mobile.app.facee.trace.TraceX

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/27 18:35
 * @description: 支持国际化的各种视图控件
 */

/**
 * 支持国际化的 TextView
 */
class I18nTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    private val i18nHelper = I18nViewHelper(context) { translatedText ->
        // 使用内部方法设置文本，避免触发动态检测
        setTextInternal(translatedText)
    }

    // 存储格式化参数
    private var formatArgs: Array<out Any>? = null

    // 标记是否是动态设置的文本
    private var isDynamicText = false

    // 标记是否已经处理过XML文本
    private var hasProcessedXmlText = false

    // 标记是否是内部文本变化
    private var isInternalTextChange = false

    // 使用TextWatcher监听文本变化，检测动态设置
    private val textWatcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        override fun afterTextChanged(s: Editable?) {
            // 如果已经处理过XML文本，且现在文本发生变化，说明是动态设置
            if (hasProcessedXmlText && !isInternalTextChange) {
                markAsDynamicText()
            }
        }
    }

    init {
        i18nHelper.initialize(attrs)

        // 添加文本监听器
        addTextChangedListener(textWatcher)

        post {
            // 只在初始化时处理XML文本，且没有被动态设置过
            if (!isDynamicText && !hasProcessedXmlText) {
                isInternalTextChange = true
                i18nHelper.processText(text?.toString())
                hasProcessedXmlText = true
                isInternalTextChange = false
            }
        }
    }



    /**
     * 内部文本设置方法，不触发动态检测
     */
    private fun setTextInternal(text: CharSequence?) {
        isInternalTextChange = true
        setText(text)
        isInternalTextChange = false
    }

    /**
     * 标记为动态文本
     */
    private fun markAsDynamicText() {
        if (!isDynamicText) {
            isDynamicText = true
        }
    }

    /**
     * 设置带格式化参数的文本
     * 用法：textView.setTextWithArgs("current_ver", "1.0.0")
     */
    fun setTextWithArgs(resourceKey: String, vararg args: Any) {
        formatArgs = args
        markAsDynamicText() // 标记为动态文本

        if (I18nManager.has(resourceKey)) {
            val template = I18nManager.get(resourceKey)
            try {
                val formattedText = String.format(template, *args)
                setTextInternal(formattedText)
            } catch (e: Exception) {
                // 格式化失败，使用原始模板
                setTextInternal(template)
            }
        } else {
            // 没有翻译，尝试从资源中获取
            try {
                val resourceId = context.resources.getIdentifier(resourceKey, "string", context.packageName)
                if (resourceId != 0) {
                    val template = context.getString(resourceId)
                    val formattedText = String.format(template, *args)
                    setTextInternal(formattedText)
                }
            } catch (e: Exception) {
                setTextInternal(resourceKey)
            }
        }
    }

    /**
     * 刷新翻译（当语言切换时调用）
     */
    fun refreshTranslation() {
        // 只有非动态文本才刷新翻译
        if (!isDynamicText) {
            post {
                i18nHelper.processText(text?.toString())
            }
        } else {
            TraceX.d("I18nTextView", "🔄 Skipping refresh for dynamic text")
        }
    }

    /**
     * 重置为XML模式（用于测试或特殊场景）
     */
    fun resetToXmlMode() {
        isDynamicText = false
        hasProcessedXmlText = false
        formatArgs = null

        post {
            i18nHelper.processText(text?.toString())
            hasProcessedXmlText = true
        }
    }

    /**
     * 安全设置文本（不触发动态检测）
     * 用于自定义控件中已经处理过国际化的文本
     */
    fun setTextSafely(text: CharSequence?) {
        setTextInternal(text)
    }
}

/**
 * 支持国际化的 EditText
 */
class I18nEditText @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatEditText(context, attrs, defStyleAttr) {

    private val i18nHelper = I18nViewHelper(context) { translatedText ->
        setTextInternal(translatedText)
    }

    private var isDynamicText = false
    private var hasProcessedXmlText = false
    private var isInternalTextChange = false

    // 使用TextWatcher监听文本变化
    private val textWatcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        override fun afterTextChanged(s: Editable?) {
            if (hasProcessedXmlText && !isInternalTextChange) {
                markAsDynamicText()
            }
        }
    }

    init {
        i18nHelper.initialize(attrs)
        addTextChangedListener(textWatcher)

        post {
            if (!isDynamicText && !hasProcessedXmlText) {
                isInternalTextChange = true
                i18nHelper.processText(text?.toString())
                hasProcessedXmlText = true
                isInternalTextChange = false
            }
        }
    }



    private fun setTextInternal(text: CharSequence?) {
        isInternalTextChange = true
        setText(text)
        isInternalTextChange = false
    }

    private fun markAsDynamicText() {
        if (!isDynamicText) {
            isDynamicText = true
        }
    }

    fun refreshTranslation() {
        if (!isDynamicText) {
            post {
                i18nHelper.processText(text?.toString())
            }
        }
    }
}

/**
 * 支持国际化的 Button
 */
class I18nButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatButton(context, attrs, defStyleAttr) {

    private val i18nHelper = I18nViewHelper(context) { translatedText ->
        setTextInternal(translatedText)
    }

    private var isDynamicText = false
    private var hasProcessedXmlText = false
    private var isInternalTextChange = false

    // 使用TextWatcher监听文本变化
    private val textWatcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        override fun afterTextChanged(s: Editable?) {
            if (hasProcessedXmlText && !isInternalTextChange) {
                markAsDynamicText()
            }
        }
    }

    init {
        i18nHelper.initialize(attrs)
        addTextChangedListener(textWatcher)

        post {
            if (!isDynamicText && !hasProcessedXmlText) {
                isInternalTextChange = true
                i18nHelper.processText(text?.toString())
                hasProcessedXmlText = true
                isInternalTextChange = false
            }
        }
    }



    private fun setTextInternal(text: CharSequence?) {
        isInternalTextChange = true
        setText(text)
        isInternalTextChange = false
    }

    private fun markAsDynamicText() {
        if (!isDynamicText) {
            isDynamicText = true
        }
    }

    fun refreshTranslation() {
        if (!isDynamicText) {
            TraceX.i("I18nButton", "🔄 Refreshing XML translation")
            post {
                i18nHelper.processText(text?.toString())
            }
        }
    }
}
