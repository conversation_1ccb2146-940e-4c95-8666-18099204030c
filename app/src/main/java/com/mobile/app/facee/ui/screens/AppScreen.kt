package com.mobile.app.facee.ui.screens

import com.slack.circuit.runtime.screen.Screen
import kotlinx.parcelize.Parcelize

/**
 * 应用主要屏幕定义
 * 遵循Circuit框架的Screen模式
 */
sealed interface AppScreen : Screen {

    /**
     * 首页 - 动态展示和推荐
     */
    @Parcelize
    data object HomeScreen : AppScreen

    /**
     * 匹配页面 - 滑动匹配功能
     */
    @Parcelize
    data object MatchScreen : AppScreen

    /**
     * 消息页面 - 聊天列表
     */
    @Parcelize
    data object MessageScreen : AppScreen

    /**
     * 个人中心 - 个人资料和设置
     */
    @Parcelize
    data object MineScreen : AppScreen

    /**
     * 聊天详情页面
     */
    @Parcelize
    data class ChatScreen(val userId: String, val userName: String) : AppScreen

    /**
     * 用户详情页面
     */
    @Parcelize
    data class UserDetailScreen(val userId: String) : AppScreen

    /**
     * 设置页面
     */
    @Parcelize
    data object SettingsScreen : AppScreen
}