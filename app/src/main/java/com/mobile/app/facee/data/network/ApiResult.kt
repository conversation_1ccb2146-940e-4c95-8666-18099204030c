package com.mobile.app.facee.data.network

/**
 * API 请求结果封装类
 * 用于统一处理网络请求的成功、失败和加载状态
 */
sealed class ApiResult<out T> {

    /**
     * 加载中状态
     */
    object Loading : ApiResult<Nothing>()

    /**
     * 成功状态
     * @param data 返回的数据
     */
    data class Success<T>(val data: T) : ApiResult<T>()

    /**
     * 错误状态
     * @param exception 异常信息
     * @param message 错误消息
     */
    data class Error(
        val code: Int = -1,
        val exception: Throwable? = null,
        val message: String = exception?.message ?: "未知错误"
    ) : ApiResult<Nothing>()

    /**
     * 判断是否为成功状态
     */
    val isSuccess: Boolean
        get() = this is Success

    /**
     * 判断是否为错误状态
     */
    val isError: Boolean
        get() = this is Error

    /**
     * 判断是否为加载状态
     */
    val isLoading: Boolean
        get() = this is Loading

    /**
     * 获取数据，如果不是成功状态则返回 null
     */
    fun getDataOrNull(): T? {
        return if (this is Success) data else null
    }

    /**
     * 获取错误信息，如果不是错误状态则返回 null
     */
    fun getErrorOrNull(): String? {
        return if (this is Error) message else null
    }
}

/**
 * 扩展函数：对 ApiResult 进行映射转换
 */
inline fun <T, R> ApiResult<T>.map(transform: (T) -> R): ApiResult<R> {
    return when (this) {
        is ApiResult.Loading -> ApiResult.Loading
        is ApiResult.Success -> ApiResult.Success(transform(data))
        is ApiResult.Error -> this
    }
}

/**
 * 扩展函数：当结果为成功时执行操作
 */
inline fun <T> ApiResult<T>.onSuccess(action: (T) -> Unit): ApiResult<T> {
    if (this is ApiResult.Success) {
        action(data)
    }
    return this
}

/**
 * 扩展函数：当结果为错误时执行操作
 */
inline fun <T> ApiResult<T>.onError(action: (String) -> Unit): ApiResult<T> {
    if (this is ApiResult.Error) {
        action(message)
    }
    return this
}

/**
 * 扩展函数：当结果为加载中时执行操作
 */
inline fun <T> ApiResult<T>.onLoading(action: () -> Unit): ApiResult<T> {
    if (this is ApiResult.Loading) {
        action()
    }
    return this
}
