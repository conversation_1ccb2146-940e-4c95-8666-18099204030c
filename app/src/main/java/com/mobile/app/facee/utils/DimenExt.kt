package com.mobile.app.facee.utils

import android.content.res.Resources
import android.util.Log
import android.util.TypedValue
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp


inline val Int.Dpi: Dp
    get() = getDimenByValue(this, "dp_")?.dp ?: this.dp

inline val Int.Spi: TextUnit
    get() = getDimenByValue(this, "sp_")?.sp ?: this.sp

fun getDimenByValue(id: Int, prefix: String): Float? {
    return try {
        val resId  = com.facee.baselibrary.R.dimen::class.java.getField("$prefix$id").getInt(null)
        Log.e("getDimenByValue", "getDrawableByName res: $resId")
        val outValue = TypedValue()
        AppContextHolder.getApplication().resources.getValue(resId, outValue, true)
        // 取出 xml 中写的 dp/sp 数值（浮点型）
        TypedValue.complexToFloat(outValue.data)
    } catch (e: Exception) {
        Log.e("getDimenByValue", "getDrawableByName failed resourceName: $id")
        e.printStackTrace()
        null
    }
}