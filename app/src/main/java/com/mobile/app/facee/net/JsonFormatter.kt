package com.mobile.app.facee.net

import org.json.JSONArray
import org.json.JSONObject


object JsonFormatter {

    /**
     * 格式化JSON字符串
     * @param json 原始JSON字符串
     * @param indent 缩进空格数，默认2
     * @return 格式化后的JSON字符串
     */
    fun formatJson(json: String?, indent: Int = 2): String {
        if (json.isNullOrBlank()) {
            return json ?: ""
        }

        return try {
            val trimmed = json.trim()
            when {
                trimmed.startsWith("{") -> {
                    val jsonObject = JSONObject(trimmed)
                    jsonObject.toString(indent)
                }
                trimmed.startsWith("[") -> {
                    val jsonArray = JSONArray(trimmed)
                    jsonArray.toString(indent)
                }
                else -> json // 不是JSON格式，直接返回原字符串
            }
        } catch (e: Exception) {
            // JSON解析失败，返回原字符串
            json
        }
    }

    /**
     * 格式化JSON字符串并添加前缀
     * @param json 原始JSON字符串
     * @param prefix 每行的前缀，如 "║   "
     * @param indent 缩进空格数，默认2
     * @return 格式化后的JSON字符串，每行都带有前缀
     */
    fun formatJsonWithPrefix(json: String?, prefix: String = "║   ", indent: Int = 2): String {
        val formatted = formatJson(json, indent)
        return if (formatted.contains('\n')) {
            // 多行JSON，为每行添加前缀
            formatted.lines().joinToString("\n") { line ->
                if (line.isBlank()) prefix.trimEnd() else "$prefix$line"
            }
        } else {
            // 单行JSON，直接添加前缀
            "$prefix$formatted"
        }
    }

    /**
     * 判断字符串是否为JSON格式
     */
    fun isJson(text: String?): Boolean {
        if (text.isNullOrBlank()) return false
        
        val trimmed = text.trim()
        return try {
            when {
                trimmed.startsWith("{") && trimmed.endsWith("}") -> {
                    JSONObject(trimmed)
                    true
                }
                trimmed.startsWith("[") && trimmed.endsWith("]") -> {
                    JSONArray(trimmed)
                    true
                }
                else -> false
            }
        } catch (e: Exception) {
            false
        }
    }
}
