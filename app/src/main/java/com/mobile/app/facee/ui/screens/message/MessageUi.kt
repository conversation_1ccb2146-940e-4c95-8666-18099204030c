package com.mobile.app.facee.ui.screens.message

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Call
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Phone
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.mobile.app.facee.R
import com.mobile.app.facee.im.CustomRongCloudConversationList
import com.mobile.app.facee.ui.components.CustomTabRow
import com.slack.circuit.runtime.ui.Ui

/**
 * 消息页面UI组件
 */
class MessageUi : Ui<MessageScreen.State> {

    @OptIn(ExperimentalFoundationApi::class)
    @Composable
    override fun Content(state: MessageScreen.State, modifier: Modifier) {
        val pagerState = rememberPagerState(
            initialPage = state.selectedTab.ordinal,
            pageCount = { MessageScreen.TabType.entries.size }
        )

        // 同步pager状态和选中的tab
        LaunchedEffect(state.selectedTab) {
            pagerState.animateScrollToPage(state.selectedTab.ordinal)
        }

        // 监听pager滑动，更新选中的tab
        LaunchedEffect(pagerState) {
            snapshotFlow { pagerState.currentPage }.collect { page ->
                val newTab = MessageScreen.TabType.entries[page]
                if (newTab != state.selectedTab) {
                    state.eventSink(MessageScreen.Event.TabSelected(newTab))
                }
            }
        }

        Column(modifier = modifier.fillMaxSize().paint(painterResource(R.mipmap.bg_app_main), contentScale = ContentScale.Crop)) {
            // 自定义Tab导航 - 靠左显示，不均分
            CustomTabRow(
                tabs = MessageScreen.TabType.entries.map { it.title },
                selectedTabIndex = state.selectedTab.ordinal,
                pagerState = pagerState,
                onTabClick = { tabIndex ->
                    val newTab = MessageScreen.TabType.entries[tabIndex]
                    state.eventSink(MessageScreen.Event.TabSelected(newTab))
                }
            )

            // HorizontalPager支持左右滑动
            HorizontalPager(
                state = pagerState,
                modifier = Modifier.fillMaxSize()
            ) { page ->
                when (MessageScreen.TabType.entries[page]) {
                    MessageScreen.TabType.MESSAGE -> {
                        MessageTabContent(state = state)
                    }
                    MessageScreen.TabType.CALLS -> {
                        CallsTabContent(state = state)
                    }
                    MessageScreen.TabType.FOLLOW -> {
                        FollowTabContent(state = state)
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MessageTabContent(state: MessageScreen.State) {
    val pullToRefreshState = rememberPullToRefreshState()

    PullToRefreshBox(
        isRefreshing = state.isLoading,
        onRefresh = { state.eventSink(MessageScreen.Event.Refresh) },
        state = pullToRefreshState,
        modifier = Modifier.fillMaxSize()
    ) {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(vertical = 8.dp)
        ) {
            // Who see me 横幅
            if (state.whoSeeMe.isNotEmpty()) {
                item {
                    WhoSeeMeSection(
                        users = state.whoSeeMe,
                        onUserClick = { user ->
                            state.eventSink(MessageScreen.Event.ViewWhoSeeMe(user.userId))
                        }
                    )
                }
            }

            // 融云IM会话列表
            item {
                CustomRongCloudConversationList(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(400.dp), // 设置固定高度，或者根据需要调整
                    onConversationClick = { rongConversation ->
                        // 将融云会话转换为应用内的会话格式
                        val targetId = rongConversation.targetId
                        val conversationTitle = rongConversation.conversationTitle ?: targetId
                        state.eventSink(MessageScreen.Event.OpenChat(targetId, conversationTitle))
                    }
                )
            }

            // 备用：自定义对话列表（如果需要显示额外的会话）
            items(state.conversations) { conversation ->
                ConversationItem(
                    conversation = conversation,
                    onClick = {
                        state.eventSink(MessageScreen.Event.OpenChat(conversation.userId, conversation.userName))
                        if (conversation.unreadCount > 0) {
                            state.eventSink(MessageScreen.Event.MarkAsRead(conversation.id))
                        }
                    }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CallsTabContent(state: MessageScreen.State) {
    val pullToRefreshState = rememberPullToRefreshState()

    PullToRefreshBox(
        isRefreshing = state.isLoading,
        onRefresh = { state.eventSink(MessageScreen.Event.Refresh) },
        state = pullToRefreshState,
        modifier = Modifier.fillMaxSize()
    ) {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(vertical = 8.dp)
        ) {
            items(state.calls.size) { index ->
                val call = state.calls[index]

                CallItem(
                    call = call,
                    onCallClick = { state.eventSink(MessageScreen.Event.CallUser(call.userId)) }
                )

                // 检查是否需要加载更多
                if (index == state.calls.size - 1 && state.calls.isNotEmpty()) {
                    // 触发加载更多
                    state.eventSink(MessageScreen.Event.LoadMore)
                }
            }

            // 加载更多指示器
            if (state.isLoading && state.calls.isNotEmpty()) {
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun FollowTabContent(state: MessageScreen.State) {
    val pullToRefreshState = rememberPullToRefreshState()

    PullToRefreshBox(
        isRefreshing = state.isLoading,
        onRefresh = { state.eventSink(MessageScreen.Event.Refresh) },
        state = pullToRefreshState,
        modifier = Modifier.fillMaxSize()
    ) {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(vertical = 8.dp)
        ) {
            items(state.follows.size) { index ->
                val follow = state.follows[index]

                FollowItem(
                    follow = follow,
                    onFollowClick = {
                        if (follow.isFollowingBack) {
                            state.eventSink(MessageScreen.Event.UnfollowUser(follow.userId))
                        } else {
                            state.eventSink(MessageScreen.Event.FollowUser(follow.userId))
                        }
                    },
                    onUserClick = { state.eventSink(MessageScreen.Event.OpenChat(follow.userId, follow.userName)) }
                )

                // 检查是否需要加载更多
                if (index == state.follows.size - 1) {
                    // 触发加载更多
                    state.eventSink(MessageScreen.Event.LoadMore)
                }
            }

            // 加载更多指示器
            if (state.isLoading && state.follows.isNotEmpty()) {
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun WhoSeeMeSection(
    users: List<MessageScreen.WhoSeeMeUser>,
    onUserClick: (MessageScreen.WhoSeeMeUser) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .clickable { /* 点击整个卡片的行为 */ },
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFFF1493).copy(alpha = 0.15f) // 深粉色背景，更接近图片
        ),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧图标和文字
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "👀",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Who see me?",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFFFF1493)
                    )
                }

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = "${users.size} people viewed your profile",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFFFF1493).copy(alpha = 0.8f)
                )
            }

            // 右侧头像列表
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy((-8).dp), // 重叠效果
                modifier = Modifier.width(120.dp) // 限制宽度
            ) {
                items(users.take(5)) { user -> // 最多显示5个头像
                    Box(
                        modifier = Modifier
                            .size(32.dp)
                            .clip(CircleShape)
                            .background(Color.White, CircleShape)
                            .padding(2.dp)
                    ) {
                        AsyncImage(
                            model = user.userAvatar,
                            contentDescription = user.userName,
                            modifier = Modifier
                                .fillMaxSize()
                                .clip(CircleShape)
                                .clickable { onUserClick(user) },
                            contentScale = ContentScale.Crop
                        )
                    }
                }

                // 如果有更多用户，显示"+N"
                if (users.size > 5) {
                    item {
                        Box(
                            modifier = Modifier
                                .size(32.dp)
                                .clip(CircleShape)
                                .background(Color(0xFFFF1493), CircleShape),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "+${users.size - 5}",
                                style = MaterialTheme.typography.bodySmall,
                                color = Color.White,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ConversationItem(
    conversation: MessageScreen.Conversation,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 头像和在线状态
        Box {
            AsyncImage(
                model = conversation.userAvatar,
                contentDescription = conversation.userName,
                modifier = Modifier
                    .size(56.dp)
                    .clip(CircleShape),
                contentScale = ContentScale.Crop
            )
            if (conversation.isOnline) {
                Box(
                    modifier = Modifier
                        .size(16.dp)
                        .align(Alignment.BottomEnd)
                        .background(Color.Green, CircleShape)
                        .background(Color.White, CircleShape)
                        .padding(2.dp)
                        .background(Color.Green, CircleShape)
                )
            }
        }

        Spacer(modifier = Modifier.width(12.dp))

        // 消息内容
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = conversation.userName,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.weight(1f)
                )
                Text(
                    text = formatMessageTime(conversation.lastMessageTime),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = conversation.lastMessage,
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (conversation.unreadCount > 0) {
                        MaterialTheme.colorScheme.onSurface
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    },
                    fontWeight = if (conversation.unreadCount > 0) FontWeight.Medium else FontWeight.Normal,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                
                if (conversation.unreadCount > 0) {
                    BadgedBox(
                        badge = {
                            Badge(
                                containerColor = MaterialTheme.colorScheme.error
                            ) {
                                Text(
                                    text = conversation.unreadCount.toString(),
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color.White
                                )
                            }
                        }
                    ) {}
                }
            }
        }
    }
}

@Composable
private fun CallItem(
    call: MessageScreen.CallRecord,
    onCallClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onCallClick() }
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 头像
        AsyncImage(
            model = call.userAvatar,
            contentDescription = call.userName,
            modifier = Modifier
                .size(56.dp)
                .clip(CircleShape),
            contentScale = ContentScale.Crop
        )

        Spacer(modifier = Modifier.width(12.dp))

        // 通话信息
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = call.userName,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.weight(1f)
                )
                Text(
                    text = formatMessageTime(call.callTime),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 通话类型和方向图标
                Icon(
                    imageVector = when {
                        call.isMissed -> Icons.Filled.Close
                        call.isIncoming -> Icons.Filled.ArrowBack
                        else -> Icons.Filled.Phone
                    },
                    contentDescription = null,
                    modifier = Modifier.size(16.dp),
                    tint = when {
                        call.isMissed -> MaterialTheme.colorScheme.error
                        else -> MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )

                Spacer(modifier = Modifier.width(4.dp))

                Text(
                    text = if (call.isMissed) "未接通话" else formatCallDuration(call.duration),
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (call.isMissed) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 通话按钮
        Icon(
            imageVector = if (call.callType == MessageScreen.CallType.VIDEO) Icons.Filled.Call else Icons.Filled.Call,
            contentDescription = "通话",
            modifier = Modifier
                .size(24.dp)
                .clickable { onCallClick() },
            tint = MaterialTheme.colorScheme.primary
        )
    }
}

@Composable
private fun FollowItem(
    follow: MessageScreen.FollowUser,
    onFollowClick: () -> Unit,
    onUserClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onUserClick() }
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 头像和在线状态
        Box {
            AsyncImage(
                model = follow.userAvatar,
                contentDescription = follow.userName,
                modifier = Modifier
                    .size(56.dp)
                    .clip(CircleShape),
                contentScale = ContentScale.Crop
            )
            if (follow.isOnline) {
                Box(
                    modifier = Modifier
                        .size(16.dp)
                        .align(Alignment.BottomEnd)
                        .background(Color.Green, CircleShape)
                        .background(Color.White, CircleShape)
                        .padding(2.dp)
                        .background(Color.Green, CircleShape)
                )
            }
        }

        Spacer(modifier = Modifier.width(12.dp))

        // 用户信息
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = follow.userName,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.weight(1f)
                )
                Text(
                    text = formatMessageTime(follow.followTime),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = follow.bio,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        // 关注按钮
        Card(
            modifier = Modifier
                .clickable { onFollowClick() },
            colors = CardDefaults.cardColors(
                containerColor = if (follow.isFollowingBack) MaterialTheme.colorScheme.surfaceVariant else MaterialTheme.colorScheme.primary
            ),
            shape = RoundedCornerShape(20.dp)
        ) {
            Text(
                text = if (follow.isFollowingBack) "已关注" else "关注",
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                style = MaterialTheme.typography.bodySmall,
                color = if (follow.isFollowingBack) MaterialTheme.colorScheme.onSurfaceVariant else Color.White
            )
        }
    }
}

private fun formatMessageTime(timestamp: Long): String {
    val now = System.currentTimeMillis()
    val diff = now - timestamp

    return when {
        diff < 60000 -> "刚刚"
        diff < 3600000 -> "${diff / 60000}分钟前"
        diff < 86400000 -> "${diff / 3600000}小时前"
        diff < 604800000 -> "${diff / 86400000}天前"
        else -> {
            val date = java.util.Date(timestamp)
            java.text.SimpleDateFormat("MM/dd", java.util.Locale.getDefault()).format(date)
        }
    }
}

private fun formatCallDuration(durationSeconds: Long): String {
    val minutes = durationSeconds / 60
    val seconds = durationSeconds % 60
    return if (minutes > 0) {
        "${minutes}分${seconds}秒"
    } else {
        "${seconds}秒"
    }
}
