package com.mobile.app.facee.di

import com.mobile.app.facee.data.service.AuthApi
import com.mobile.app.facee.data.network.NetworkClient
import com.mobile.app.facee.data.repository.AuthRepository

/**
 * 应用依赖模块
 * 提供应用所需的各种依赖实例
 */
object AppModule {

    /**
     * 提供AuthApi实例
     */
    fun provideAuthApi(): AuthApi {
        return NetworkClient.createService(AuthApi::class.java)
    }

    /**
     * 提供AuthRepository实例
     */
    fun provideAuthRepository(): AuthRepository {
        return AuthRepository(provideAuthApi())
    }
}
