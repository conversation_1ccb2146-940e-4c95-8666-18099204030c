package com.mobile.app.facee.ui.screens

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import com.slack.circuit.runtime.Navigator
import com.slack.circuit.runtime.presenter.Presenter

/**
 * 主屏幕Presenter
 * 负责管理底部导航的状态
 */
class MainPresenter(
    private val navigator: Navigator
) : Presenter<MainScreen.State> {

    @Composable
    override fun present(): MainScreen.State {
        var selectedTab by remember { mutableStateOf(MainScreen.TabItem.HOME) }

        return MainScreen.State(
            selectedTab = selectedTab,
            eventSink = { event ->
                when (event) {
                    is MainScreen.Event.TabSelected -> {
                        selectedTab = event.tab
                    }
                }
            }
        )
    }
}
