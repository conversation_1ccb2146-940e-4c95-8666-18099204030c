package com.mobile.app.facee.trace

import android.util.Log
import com.mobile.app.facee.BuildConfig
import com.mobile.app.facee.utils.AppContextHolder
import com.mobile.app.logger.TraceXUploadQueueManager
import com.orhanobut.logger.AndroidLogAdapter
import com.orhanobut.logger.LogAdapter
import com.orhanobut.logger.LogcatLogStrategy
import com.orhanobut.logger.Logger
import java.io.BufferedWriter
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStreamWriter
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

typealias LogX = TraceX

object TraceX {
    private var MAX_SIZE =
        if (BuildConfig.DEBUG) 1000 * 1024 /* 100KB */ else 1 * 1024 * 1024 // 1MB
    private val logDir by lazy {
        File(AppContextHolder.getApplication().filesDir, "logs").apply { mkdirs() }
    }

    private var writer: BufferedWriter? = null
    private var currentFile: File? = null

    // 文件状态跟踪
    private val uploadingFiles = mutableSetOf<String>() // 正在上传的文件名
    private val pendingDeleteFiles = mutableSetOf<String>() // 等待删除的文件名
    private var isCurrentFileUploading = false

    // 用于同步的锁
    private val fileLock = Any()

    init {
        Logger.addLogAdapter(AndroidLogAdapter())
        initializeLogFile()
    }

    /**
     * 初始化日志文件
     * 检查是否有未完成的日志文件，如果有则继续使用，否则创建新文件
     */
    private fun initializeLogFile() {
        synchronized(fileLock) {
            try {
                // 首先处理已达到阈值的文件
                processOversizedFiles()

                // 查找未完成的日志文件（小于阈值的文件）
                val incompleteFile = findIncompleteLogFile()

                if (incompleteFile != null) {
                    // 继续使用未完成的文件，指定 UTF-8 编码
                    currentFile = incompleteFile
                    writer = BufferedWriter(
                        OutputStreamWriter(
                            FileOutputStream(incompleteFile, true), Charsets.UTF_8
                        )
                    )
                    Logger.d("LogX: 继续使用未完成的日志文件: ${incompleteFile.name}, 当前大小: ${incompleteFile.length()} bytes")
                } else {
                    // 创建新的日志文件
                    createNewLogFile()
                }
            } catch (e: Exception) {
                Logger.e("LogX: 初始化日志文件失败: ${e.message}")
                e.printStackTrace()
                // 出错时创建新文件
                createNewLogFile()
            }
        }
    }

    /**
     * 查找未完成的日志文件（小于阈值且未在上传中的文件）
     */
    private fun findIncompleteLogFile(): File? {
        return try {
            logDir.listFiles { file ->
                file.isFile && file.name.startsWith("log_") && file.name.endsWith(".txt") && file.length() < MAX_SIZE && file.length() > 0 && !uploadingFiles.contains(
                    file.name
                ) // 排除正在上传的文件
            }?.maxByOrNull { it.lastModified() } // 选择最新的未完成文件
        } catch (e: Exception) {
            Logger.e("LogX: 查找未完成文件失败: ${e.message}")
            null
        }
    }

    /**
     * 处理已达到阈值的文件，将其加入上传队列
     */
    private fun processOversizedFiles() {
        try {
            logDir.listFiles { file ->
                file.isFile && file.name.startsWith("log_") && file.name.endsWith(".txt") && file.length() >= MAX_SIZE && !uploadingFiles.contains(
                    file.name
                ) // 排除已在上传中的文件
            }?.forEach { file ->
                Logger.d("LogX: 发现超大文件，标记为上传中: ${file.name}, 大小: ${file.length()} bytes")
                markFileAsUploading(file)
                startUpload(file)
            }
        } catch (e: Exception) {
            Logger.e("LogX: 处理超大文件失败: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 标记文件为上传中状态
     */
    private fun markFileAsUploading(file: File) {
        synchronized(fileLock) {
            uploadingFiles.add(file.name)
            Logger.d("LogX: 标记文件为上传中: ${file.name}")
        }
    }

    /**
     * 标记文件上传完成，可以删除
     */
    private fun markFileUploadComplete(file: File) {
        synchronized(fileLock) {
            uploadingFiles.remove(file.name)
            pendingDeleteFiles.add(file.name)
            Logger.d("LogX: 标记文件上传完成，等待删除: ${file.name}")

            // 立即删除文件
            try {
                if (file.exists()) {
                    file.delete()
                    Logger.d("LogX: 文件删除成功: ${file.name}")
                }
                pendingDeleteFiles.remove(file.name)
            } catch (e: Exception) {
                Logger.e("LogX: 文件删除失败: ${file.name}, error: ${e.message}")
            }
        }
    }

    /**
     * 标记文件上传失败
     */
    private fun markFileUploadFailed(file: File) {
        synchronized(fileLock) {
            uploadingFiles.remove(file.name)
            Logger.w("LogX: 文件上传失败，移除上传标记: ${file.name}")
        }
    }

    /**
     * 开始上传文件
     */
    private fun startUpload(file: File) {
        TraceXUploadQueueManager.enqueue(file) { success ->
            if (success) {
                markFileUploadComplete(file)
            } else {
                markFileUploadFailed(file)
            }
        }
    }

    /**
     * 创建新的日志文件
     */
    private fun createNewLogFile() {
        try {
            // 关闭当前 writer
            writer?.apply {
                flush()
                close()
            }
            writer = null

            // 生成新的文件名（包含毫秒确保唯一性）
            val timestamp =
                SimpleDateFormat("yyyyMMdd_HHmmss_SSS", Locale.getDefault()).format(Date())
            currentFile = File(logDir, "log_${timestamp}.txt")

            // 创建新的 writer，指定 UTF-8 编码
            writer = currentFile?.bufferedWriter(Charsets.UTF_8)

            Logger.d("LogX: 创建新日志文件: ${currentFile?.name}")
        } catch (e: Exception) {
            Logger.e("LogX: 创建日志文件失败: ${e.message}")
            e.printStackTrace()
        }
    }

    fun i(str: String?) {
        Logger.i(str ?: "")
        log(str ?: "")
    }

    fun i(tag: String, str: String?) {
        Logger.i(tag, str ?: "")
        log(str ?: "")
    }

    fun v(str: String?) {
        Logger.v(str ?: "")
        log(str ?: "")
    }

    fun d(str: String?) {
        Logger.d(str ?: "")
        log(str ?: "")
    }

    fun d(tag: String, str: String?) {
        Logger.d(tag, str ?: "")
        log(str ?: "")
    }

    fun w(str: String?) {
        Logger.w(str ?: "")
        log(str ?: "")
    }

    fun w(tag: String, str: String?) {
        Logger.w(tag, str ?: "")
        log(str ?: "")
    }

    fun e(str: String?) {
        Logger.e(str ?: "")
        log(str ?: "")
    }

    fun e(tag: String, str: String?) {
        Logger.e(tag, str ?: "")
        log(str ?: "")
    }

    fun e(tag: String, e: Throwable) {
        Logger.e(tag, Log.getStackTraceString(e))
        log(Log.getStackTraceString(e))
    }

    fun json(str: String?) {
        Logger.json(str)
        log(str ?: "")
    }

    fun log(message: String) {
        synchronized(fileLock) {
            try {
                // 确保有可用的日志文件
                ensureLogFileAvailable()

                val time =
                    SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault()).format(Date())
                val content = "$time: $message\n"

                writer?.apply {
                    write(content)
                    flush()
                }

                // 检查文件大小，如果达到上限则轮转
                currentFile?.let { file ->
                    if (file.length() >= MAX_SIZE) {
                        Logger.d("LogX: 文件大小达到上限 (${file.length()} bytes)，开始轮转")
                        rotateLogFile()
                    }
                }
            } catch (e: Exception) {
                Logger.e("LogX: 写入日志失败: ${e.message}")
                e.printStackTrace()
                // 出错时尝试重新初始化
                try {
                    initializeLogFile()
                } catch (e2: Exception) {
                    Logger.e("LogX: 重新初始化也失败: ${e2.message}")
                }
            }
        }
    }

    /**
     * 确保有可用的日志文件
     */
    private fun ensureLogFileAvailable() {
        // 如果当前文件正在上传，需要找到或创建新文件
        if (isCurrentFileUploading) {
            Logger.w("LogX: 当前文件正在上传，寻找可用文件")
            val incompleteFile = findIncompleteLogFile()
            if (incompleteFile != null) {
                currentFile = incompleteFile
                writer = BufferedWriter(
                    OutputStreamWriter(
                        FileOutputStream(incompleteFile, true), Charsets.UTF_8
                    )
                )
                Logger.d("LogX: 切换到未完成文件: ${incompleteFile.name}")
            } else {
                createNewLogFile()
            }
        }

        // 如果当前文件不存在或 writer 为空，重新初始化
        if (currentFile == null || !currentFile!!.exists() || writer == null) {
            Logger.w("LogX: 当前文件不可用，重新初始化")
            initializeLogFile()
        }
    }

    /**
     * 轮转日志文件
     * 关闭当前文件，标记为上传中，创建新文件继续写入
     */
    private fun rotateLogFile() {
        synchronized(fileLock) {
            try {
                val fileToUpload = currentFile

                // 确保 writer 正确关闭并刷新
                writer?.apply {
                    flush()
                    close()
                }
                writer = null
                currentFile = null

                // 检查文件是否存在且达到阈值
                fileToUpload?.let { file ->
                    Logger.d("LogX: 检查待轮转文件: ${file.name}, 存在: ${file.exists()}, 大小: ${file.length()} bytes")
                    if (file.exists() && file.length() >= MAX_SIZE) {
                        Logger.d("LogX: 文件达到阈值，标记为上传中: ${file.name}, 大小: ${file.length()} bytes")

                        // 标记文件为上传中，禁止再次操作
                        markFileAsUploading(file)

                        // 开始上传
                        startUpload(file)
                    } else {
                        Logger.w("LogX: 文件不满足轮转条件: ${file.absolutePath}, 存在: ${file.exists()}, 大小: ${file.length()}")
                    }
                } ?: Logger.w("LogX: 待轮转文件为null")

                // 寻找可用的未完成文件（排除上传中的文件）
                val incompleteFile = findIncompleteLogFile()
                if (incompleteFile != null) {
                    currentFile = incompleteFile
                    writer = BufferedWriter(
                        OutputStreamWriter(
                            FileOutputStream(incompleteFile, true), Charsets.UTF_8
                        )
                    )
                    Logger.d("LogX: 轮转后使用未完成文件: ${incompleteFile.name}, 大小: ${incompleteFile.length()} bytes")
                } else {
                    // 没有可用的未完成文件，创建新文件
                    createNewLogFile()
                }
            } catch (e: Exception) {
                Logger.e("LogX: 日志文件轮转失败: ${e.message}")
                e.printStackTrace()
                // 确保即使出错也能创建新文件
                try {
                    createNewLogFile()
                } catch (e2: Exception) {
                    Logger.e("LogX: 创建新文件也失败了: ${e2.message}")
                }
            }
        }
    }

    /**
     * 强制上传当前日志文件
     */
    fun forceUpload() {
        synchronized(fileLock) {
            try {
                val fileToUpload = currentFile

                // 确保 writer 正确关闭并刷新
                writer?.apply {
                    flush()
                    close()
                }
                writer = null
                currentFile = null

                // 检查文件是否存在且有内容
                fileToUpload?.let { file ->
                    if (file.exists() && file.length() > 0) {
                        Logger.d("LogX: 强制上传当前文件: ${file.name}, 大小: ${file.length()} bytes")

                        // 标记文件为上传中
                        markFileAsUploading(file)

                        // 开始上传
                        startUpload(file)
                    } else {
                        Logger.w("LogX: 强制上传时文件不存在或为空: ${file?.absolutePath}")
                    }
                }

                // 寻找可用的未完成文件（排除上传中的文件）
                val incompleteFile = findIncompleteLogFile()
                if (incompleteFile != null) {
                    currentFile = incompleteFile
                    writer = BufferedWriter(
                        OutputStreamWriter(
                            FileOutputStream(incompleteFile, true), Charsets.UTF_8
                        )
                    )
                    Logger.d("LogX: 强制上传后使用未完成文件: ${incompleteFile.name}, 大小: ${incompleteFile.length()} bytes")
                } else {
                    createNewLogFile()
                }
            } catch (e: Exception) {
                Logger.e("LogX: 强制上传失败: ${e.message}")
                e.printStackTrace()
                // 确保即使出错也能创建新文件
                try {
                    createNewLogFile()
                } catch (e2: Exception) {
                    Logger.e("LogX: 创建新文件失败: ${e2.message}")
                }
            }
        }
    }

    /**
     * 清理所有日志文件（用于测试）
     */
    fun clearAllLogs() {
        synchronized(fileLock) {
            try {
                writer?.close()
                writer = null
                currentFile = null

                logDir.listFiles()?.forEach { file ->
                    if (file.isFile) {
                        file.delete()
                        Logger.d("LogX: 删除日志文件: ${file.name}")
                    }
                }

                createNewLogFile()
                Logger.d("LogX: 所有日志文件已清理")
            } catch (e: Exception) {
                Logger.e("LogX: 清理日志文件失败: ${e.message}")
                e.printStackTrace()
            }
        }
    }
}
