package com.mobile.app.facee.data.repository

import com.mobile.app.facee.data.network.ApiResult
import com.mobile.app.facee.data.network.NetworkClient
import com.mobile.app.facee.data.service.ApiService
import com.mobile.app.facee.manager.FileUploadManager
import com.mobile.app.facee.trace.LogX
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 文件相关数据仓库
 * 处理文件上传相关的网络请求
 */
class FileRepository {

    private val apiService = NetworkClient.createService(ApiService::class.java)

    /**
     * 获取AWS S3上传链接
     */
    suspend fun getUploadUrl(
        bizType: FileUploadManager.BizType = FileUploadManager.BizType.NORMAL_UPLOAD,
        uploadFileType: FileUploadManager.FileType
    ): ApiResult<FileUploadManager.UploadCredentialBean> = withContext(Dispatchers.IO) {
        try {
            LogX.d("获取上传链接: bizType=$bizType, uploadFileType=$uploadFileType")
            val response = if (uploadFileType == FileUploadManager.FileType.LOG) {
                apiService.getUploadLogUrl(mapOf("ext" to uploadFileType.extension))
            } else {
                apiService.getUploadUrl(
                    bizType = bizType.value,
                    extension = uploadFileType.extension
                )
            }

            if (response.isSuccess) {
                if (response.data != null) {
                    LogX.d("获取上传链接成功: ${response.data.uploadURL}")
                    ApiResult.Success(response.data)
                } else {
                    LogX.e("获取上传链接失败: ${response.message}")
                    ApiResult.Error(message = response.message)
                }
            } else {
                LogX.e("获取上传链接网络请求失败: ${response.code}")
                ApiResult.Error(message = "Failed to obtain upload link: ${response.code}")
            }
        } catch (e: Exception) {
            LogX.e("获取上传链接异常", e.message)
            ApiResult.Error(exception = e, message = "Abnormal retrieval of upload link: ${e.message}")
        }
    }
}
