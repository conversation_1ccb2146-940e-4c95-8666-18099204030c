package com.mobile.app.facee

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.runtime.Composable
import com.mobile.app.facee.data.network.AuthManager
import com.mobile.app.facee.di.provideCircuitConfig
import com.mobile.app.facee.ui.navigation.RootNavHost
import com.mobile.app.facee.ui.screens.MainScreen
import com.mobile.app.facee.ui.screens.auth.LoginScreen
import com.mobile.app.facee.ui.theme.FaceeTheme
import com.slack.circuit.backstack.rememberSaveableBackStack
import com.slack.circuit.foundation.CircuitCompositionLocals
import com.slack.circuit.foundation.rememberCircuitNavigator

/**
 * 主Activity
 * 应用的入口点，设置Circuit框架和主题
 */
class MainActivity : ComponentActivity() {

    private val circuitConfig by lazy { provideCircuitConfig() }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        setContent {
            FaceeTheme {
                FaceeApp()
            }
        }
    }

    @Composable
    private fun FaceeApp() {
        // 根据登录状态决定起始屏幕
        val startScreen = if (AuthManager.isLoggedIn()) {
            MainScreen
        } else {
            LoginScreen
        }

        val backStack = rememberSaveableBackStack(startScreen)
        val navigator = rememberCircuitNavigator(
            backStack = backStack, onRootPop = {
                // 如果在根屏幕按返回键，退出应用
                finish()
            })

        CircuitCompositionLocals(circuit = circuitConfig) {
            RootNavHost(startScreen = startScreen, navigator = navigator)
        }
    }
}