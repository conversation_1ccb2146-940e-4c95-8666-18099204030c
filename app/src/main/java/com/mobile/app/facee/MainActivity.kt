package com.mobile.app.facee

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.mobile.app.facee.di.provideCircuitConfig
import com.mobile.app.facee.ui.navigation.RootNavHost
import com.mobile.app.facee.ui.screens.MainScreen
import com.mobile.app.facee.ui.theme.FaceeTheme
import com.slack.circuit.foundation.CircuitCompositionLocals
import com.slack.circuit.foundation.rememberCircuitNavigator
import com.slack.circuit.backstack.rememberSaveableBackStack

/**
 * 主Activity
 * 应用的入口点，设置Circuit框架和主题
 */
class MainActivity : ComponentActivity() {

    private val circuitConfig by lazy { provideCircuitConfig() }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        setContent {
            FaceeTheme {
                FaceeApp()
            }
        }
    }

    @Composable
    private fun FaceeApp() {
        val backStack = rememberSaveableBackStack(MainScreen)
        val navigator = rememberCircuitNavigator(
            backStack = backStack,
            onRootPop = { /* Handle root pop */ }
        )

        CircuitCompositionLocals(circuit = circuitConfig) {
            RootNavHost(startScreen = MainScreen)
        }
    }
}