package com.mobile.app.facee.data.network

import android.content.Context
import android.os.Build
import com.mobile.app.facee.manager.DataStoreManager
import com.mobile.app.facee.trace.LogX
import com.mobile.app.facee.utils.AppContextHolder
import kotlinx.coroutines.runBlocking

/**
 * 鉴权管理器
 * 负责管理API请求的鉴权信息
 * 使用 ActivityUtils 获取 Context
 */
object AuthManager {

    // 请求头常量
    const val HEADER_AUTHORIZATION = "Authorization"
    const val HEADER_USER_AGENT = "User-Agent"
    const val HEADER_CONTENT_TYPE = "Content-Type"
    const val HEADER_ACCEPT = "Accept"

    // 默认值
    private const val DEFAULT_API_KEY = "your_api_key_here"
    private const val DEFAULT_CLIENT_ID = "anchor_client"

    /**
     * 获取 Context
     */
    private fun getContext(): Context {
        return AppContextHolder.getApplication()
    }

    /**
     * 获取访问令牌
     */
    fun getAccessToken(): String? {
        return DataStoreManager.getAccessTokenSync()
    }

    /**
     * 获取刷新令牌
     */
    fun getRefreshToken(): String? {
        return DataStoreManager.getRefreshTokenSync()
    }

    /**
     * 保存令牌
     */
    suspend fun saveTokens(accessToken: String, refreshToken: String? = null) {
        DataStoreManager.saveTokens(accessToken, refreshToken)
        LogX.d("令牌已保存")
    }

    /**
     * 清除令牌
     */
    suspend fun clearTokens() {
        DataStoreManager.clearUserData()
        LogX.d("令牌已清除")
    }

    /**
     * 检查是否已登录
     */
    fun isLoggedIn(): Boolean {
        return !getAccessToken().isNullOrEmpty()
    }

    /**
     * 获取API密钥
     */
    fun getApiKey(): String {
        return runBlocking { DataStoreManager.getString("api_key") } ?: DEFAULT_API_KEY
    }

    /**
     * 设置API密钥
     */
    fun setApiKey(apiKey: String) {
        runBlocking { DataStoreManager.putString("api_key", apiKey) }
    }

    /**
     * 获取客户端ID
     */
    fun getClientId(): String {
        return runBlocking { DataStoreManager.getString("client_id") } ?: DEFAULT_CLIENT_ID
    }

    /**
     * 设置客户端ID
     */
    fun setClientId(clientId: String) {
        runBlocking { DataStoreManager.putString("client_id", clientId) }
    }

    /**
     * 生成时间戳
     */
    fun generateTimestamp(): String {
        return System.currentTimeMillis().toString()
    }

    /**
     * 生成签名（简单示例，实际项目中应该使用更安全的签名算法）
     */
    fun generateSignature(timestamp: String, data: String = ""): String {
        val apiKey = getApiKey()
        val clientId = getClientId()
        val rawString = "$clientId$timestamp$data$apiKey"

        // 这里使用简单的哈希，实际项目中应该使用HMAC-SHA256等
        return rawString.hashCode().toString()
    }

    /**
     * 获取User-Agent
     */
    fun getUserAgent(): String {
        val context = getContext()
        val appVersion = try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName ?: "1.0.0"
        } catch (e: Exception) {
            "1.0.0"
        }

        val deviceModel = Build.MODEL
        val osVersion = Build.VERSION.RELEASE

        return "Anchor/$appVersion (Android $osVersion; $deviceModel)"
    }

    /**
     * 创建鉴权请求头
     */
    fun createAuthHeaders(includeSignature: Boolean = true): Map<String, String> {
        val headers = mutableMapOf<String, String>()

        // 基础请求头 - 移除Content-Type，让OkHttp自动处理
        headers[HEADER_ACCEPT] = "application/json"
        headers[HEADER_USER_AGENT] = getUserAgent()

        // 访问令牌
        getAccessToken()?.let { token ->
            headers[HEADER_AUTHORIZATION] = token
        }

        // 时间戳和签名
        if (includeSignature) {
            val timestamp = generateTimestamp()
        }

        return headers
    }

    /**
     * 创建登录请求头（不包含访问令牌）
     */
    fun createLoginHeaders(): Map<String, String> {
        val headers = mutableMapOf<String, String>()

        // 移除Content-Type，让OkHttp自动处理
        headers[HEADER_ACCEPT] = "application/json"
        headers[HEADER_USER_AGENT] = getUserAgent()

        return headers
    }

    /**
     * 验证令牌是否有效（简单检查）
     */
    fun isTokenValid(): Boolean {
        val token = getAccessToken()
        if (token.isNullOrEmpty()) return false

        // 这里可以添加更复杂的令牌验证逻辑
        // 比如检查令牌格式、过期时间等

        return true
    }

    /**
     * 令牌即将过期时的处理
     */
    fun handleTokenExpiring() {
        LogX.d("令牌即将过期，需要刷新")
        // 这里可以添加自动刷新令牌的逻辑
    }

    /**
     * 令牌过期时的处理
     */
    fun handleTokenExpired() {
        LogX.d("令牌已过期，清除本地令牌")
        runBlocking { clearTokens() }
        // 这里可以添加跳转到登录页面的逻辑
    }
}
