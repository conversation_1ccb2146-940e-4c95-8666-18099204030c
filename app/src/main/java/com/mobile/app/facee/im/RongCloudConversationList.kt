package com.mobile.app.facee.im

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidViewBinding
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentContainerView
import androidx.fragment.app.commit
import io.rong.imkit.conversation.ConversationListFragment
import io.rong.imlib.model.Conversation

/**
 * 融云会话列表Compose组件
 * 将融云的ConversationListFragment集成到Compose中
 */
@Composable
fun RongCloudConversationList(
    modifier: Modifier = Modifier,
    onConversationClick: ((Conversation) -> Unit)? = null
) {
    val context = LocalContext.current
    val activity = context as? FragmentActivity
    
    if (activity == null) {
        // 如果不是FragmentActivity，显示错误或空状态
        return
    }

    val fragmentContainerView = remember {
        FragmentContainerView(context).apply {
            id = android.view.View.generateViewId()
        }
    }

    val conversationListFragment = remember {
        ConversationListFragment().apply {
            // 设置会话类型，这里设置为私聊
            setAdapter(Conversation.ConversationType.PRIVATE)
            
            // 设置会话点击监听器
            onConversationClick?.let { clickListener ->
                setOnConversationClickListener { _, conversation ->
                    clickListener(conversation)
                    false // 返回false表示不拦截默认行为
                }
            }
        }
    }

    DisposableEffect(activity) {
        // 添加Fragment到Activity
        activity.supportFragmentManager.commit {
            replace(fragmentContainerView.id, conversationListFragment)
        }

        onDispose {
            // 清理Fragment
            activity.supportFragmentManager.commit {
                remove(conversationListFragment)
            }
        }
    }

    androidx.compose.ui.viewinterop.AndroidView(
        factory = { fragmentContainerView },
        modifier = modifier.fillMaxSize()
    )
}

/**
 * 融云会话列表Fragment包装器
 * 提供更多自定义选项
 */
class CustomConversationListFragment : ConversationListFragment() {
    
    private var conversationClickListener: ((Conversation) -> Unit)? = null
    
    fun setConversationClickListener(listener: (Conversation) -> Unit) {
        conversationClickListener = listener
    }
    
    override fun onResume() {
        super.onResume()
        
        // 设置会话点击监听
        conversationClickListener?.let { listener ->
            setOnConversationClickListener { _, conversation ->
                listener(conversation)
                false
            }
        }
    }
}

/**
 * 使用自定义Fragment的Compose组件
 */
@Composable
fun CustomRongCloudConversationList(
    modifier: Modifier = Modifier,
    onConversationClick: ((Conversation) -> Unit)? = null
) {
    val context = LocalContext.current
    val activity = context as? FragmentActivity
    
    if (activity == null) {
        return
    }

    val fragmentContainerView = remember {
        FragmentContainerView(context).apply {
            id = android.view.View.generateViewId()
        }
    }

    val conversationListFragment = remember {
        CustomConversationListFragment().apply {
            // 设置会话类型
            setAdapter(Conversation.ConversationType.PRIVATE)
            
            // 设置点击监听器
            onConversationClick?.let { clickListener ->
                setConversationClickListener(clickListener)
            }
        }
    }

    DisposableEffect(activity) {
        activity.supportFragmentManager.commit {
            replace(fragmentContainerView.id, conversationListFragment)
        }

        onDispose {
            activity.supportFragmentManager.commit {
                remove(conversationListFragment)
            }
        }
    }

    androidx.compose.ui.viewinterop.AndroidView(
        factory = { fragmentContainerView },
        modifier = modifier.fillMaxSize()
    )
}
