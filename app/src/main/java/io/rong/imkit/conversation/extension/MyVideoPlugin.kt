package io.rong.imkit.conversation.extension

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import com.mobile.app.facee.R
import io.rong.imkit.conversation.extension.component.plugin.IPluginModule


class MyVideoPlugin : IPluginModule {
    override fun obtainDrawable(context: Context): Drawable? =
        ResourcesCompat.getDrawable(context.resources, R.mipmap.icon_message_plugin_video, null)

    override fun obtainTitle(context: Context): String = context.getString(R.string.desc_video)

    override fun onClick(currentFragment: Fragment, extension: RongExtension, index: Int) {
//            currentFragment.activity?.let {
//                val myRongConversationActivity = it as MyRongConversationActivity
//                myRongConversationActivity.startVideo()
//            }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent) {
    }
}