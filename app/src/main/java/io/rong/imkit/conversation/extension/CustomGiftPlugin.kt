package io.rong.imkit.conversation.extension

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import com.mobile.app.facee.R
import io.rong.imkit.conversation.extension.component.plugin.IPluginModule

/**
 * Author:Lxf
 * Create on:2024/8/6
 * Description:
 */
class CustomGiftPlugin : IPluginModule {
//    private var giftPopup: BasePopupView? = null
    override fun obtainDrawable(context: Context): Drawable? =
        ResourcesCompat.getDrawable(context.resources, R.mipmap.icon_message_plugin_gift, null)

    override fun obtainTitle(context: Context): String = context.getString(R.string.desc_gift)

    override fun onClick(currentFragment: Fragment, extension: RongExtension, index: Int) {
//        currentFragment.activity?.let {
//            giftPopup = giftPopup?.show() ?: showGiftPopup(
//                it,
//                (it as? MyRongConversationActivity)?.getTargetId()
//            ) { giftBean ->
//                val myRongConversationActivity = it as MyRongConversationActivity
//                myRongConversationActivity.sendGift(giftBean)
//            }
//        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent) {
    }
}