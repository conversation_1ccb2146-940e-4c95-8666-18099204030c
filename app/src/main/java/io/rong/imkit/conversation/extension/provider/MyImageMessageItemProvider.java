package io.rong.imkit.conversation.extension.provider;

import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.text.Spannable;
import android.text.SpannableString;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestBuilder;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;

import io.rong.common.rlog.RLog;
import io.rong.imkit.IMCenter;
import io.rong.imkit.R.drawable;

import com.mobile.app.facee.AppConstants;
import com.mobile.app.facee.R;

import io.rong.imkit.R.string;
import io.rong.imkit.activity.PicturePagerActivity;
import io.rong.imkit.conversation.messgelist.provider.BaseMessageItemProvider;
import io.rong.imkit.feature.resend.ResendManager;
import io.rong.imkit.model.UiMessage;
import io.rong.imkit.picture.tools.ScreenUtils;
import io.rong.imkit.widget.adapter.IViewProviderListener;
import io.rong.imkit.widget.adapter.ViewHolder;
import io.rong.imlib.model.Message;
import io.rong.imlib.model.MessageContent;
import io.rong.imlib.model.Message.MessageDirection;
import io.rong.message.ImageMessage;

import java.util.List;
import java.util.Objects;

public class MyImageMessageItemProvider extends BaseMessageItemProvider<ImageMessage> {
    private static final String TAG = "ImageMessageItemProvide";
    private static int THUMB_COMPRESSED_SIZE = 240;
    private static int THUMB_COMPRESSED_MIN_SIZE = 100;
    private final String MSG_TAG = "RC:ImgMsg";
    private Integer minSize = null;
    private Integer maxSize = null;

    public MyImageMessageItemProvider() {
        this.mConfig.showContentBubble = false;
        this.mConfig.showProgress = false;
        this.mConfig.showReadState = true;
        this.mConfig.showWarning = false;
        Context context = IMCenter.getInstance().getContext();
        if (context != null) {
            Resources resources = context.getResources();

            try {
                THUMB_COMPRESSED_SIZE = resources.getInteger(resources.getIdentifier("rc_thumb_compress_size", "integer", context.getPackageName()));
                THUMB_COMPRESSED_MIN_SIZE = resources.getInteger(resources.getIdentifier("rc_thumb_compress_min_size", "integer", context.getPackageName()));
            } catch (Resources.NotFoundException e) {
                e.printStackTrace();
            }
        }
    }

    protected ViewHolder onCreateMessageContentViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.rc_message_image_item, parent, false);
        return new ViewHolder(view.getContext(), view);
    }

    protected void bindMessageContentViewHolder(final ViewHolder holder, ViewHolder parentHolder, ImageMessage message, UiMessage uiMessage, int position, List<UiMessage> list, IViewProviderListener<UiMessage> listener) {
        final ImageView view = holder.getView(R.id.rc_image);
        if (!this.checkViewsValid(new View[]{view})) {
            RLog.e("ImageMessageItemProvide", "checkViewsValid error," + uiMessage.getObjectName());
        } else {
            //设置违规拦截图片提示
            final ImageView block = holder.getView(R.id.rc_image_block);
            if (uiMessage.getExpansion() != null) {
                if (uiMessage.getExpansion().containsKey(AppConstants.MESSAGE_EXPEND_KEY_BLOCK) && Objects.equals(uiMessage.getExpansion().get(AppConstants.MESSAGE_EXPEND_KEY_BLOCK), "1")) {
                    block.setVisibility(View.VISIBLE);
                } else {
                    block.setVisibility(View.GONE);
                }
            }
//            block.setOnClickListener(v -> ToastUtil.show(v.getContext().getString(R.string.rc_image_block_tips)));

            Uri thumUri = message.getThumUri();
            if (uiMessage.getState() != 2 && (uiMessage.getState() != 1 || !ResendManager.getInstance().needResend(uiMessage.getMessageId()))) {
                holder.setVisible(R.id.rl_progress, false);
                holder.setVisible(R.id.main_bg, false);
            } else {
                holder.setVisible(R.id.rl_progress, true);
                holder.setVisible(R.id.main_bg, true);
                holder.setText(R.id.tv_progress, uiMessage.getProgress() + "%");
            }

            if (thumUri != null && thumUri.getPath() != null) {
                RequestOptions options = (RequestOptions) RequestOptions.bitmapTransform(new RoundedCorners(ScreenUtils.dip2px(IMCenter.getInstance().getContext(), 6.0F))).override(Integer.MIN_VALUE, Integer.MIN_VALUE);
                ((RequestBuilder) Glide.with(view).load(thumUri.getPath()).error(uiMessage.getMessage().getMessageDirection() == MessageDirection.SEND ? drawable.rc_send_thumb_image_broken : drawable.rc_received_thumb_image_broken)).apply(options).listener(new RequestListener<Drawable>() {
                    public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                        mConfig.showWarning = true;
                        ViewGroup.LayoutParams params = view.getLayoutParams();
                        params.height = ScreenUtils.dip2px(view.getContext(), 35.0F);
                        params.width = ScreenUtils.dip2px(view.getContext(), 35.0F);
                        view.setLayoutParams(params);
                        return false;
                    }

                    public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                        mConfig.showWarning = true;
                        measureLayoutParams(holder.getView(R.id.rl_content), resource);
                        return false;
                    }
                }).into(view);
            } else {
                mConfig.showWarning = true;
                ViewGroup.LayoutParams params = view.getLayoutParams();
                params.height = ScreenUtils.dip2px(view.getContext(), 35.0F);
                params.width = ScreenUtils.dip2px(view.getContext(), 35.0F);
                view.setLayoutParams(params);
                view.setImageResource(uiMessage.getMessage().getMessageDirection() == MessageDirection.SEND ? drawable.rc_send_thumb_image_broken : drawable.rc_received_thumb_image_broken);
            }

        }
    }

    protected boolean onItemClick(ViewHolder holder, ImageMessage imageMessage, UiMessage uiMessage, int position, List<UiMessage> list, IViewProviderListener<UiMessage> listener) {
        Message message = uiMessage.getMessage();
        if (message != null && message.getContent() != null) {
            Intent intent = new Intent(holder.getContext(), PicturePagerActivity.class);
            intent.putExtra("message", message);
            holder.getContext().startActivity(intent);
            return true;
        } else {
            RLog.e("ImageMessageItemProvide", "onItemClick error, message or message content is null");
            return false;
        }
    }

    protected boolean isMessageViewType(MessageContent messageContent) {
        return messageContent instanceof ImageMessage && !messageContent.isDestruct();
    }

    private void measureLayoutParams(View view, Drawable drawable) {
        if (view != null) {
            int width = drawable.getIntrinsicWidth();
            int height = drawable.getIntrinsicHeight();
            if (minSize == null) {
                minSize = THUMB_COMPRESSED_MIN_SIZE;
            }

            if (maxSize == null) {
                maxSize = THUMB_COMPRESSED_SIZE;
            }

            int finalWidth;
            int finalHeight;
            if (width >= minSize && height >= minSize) {
                if (width < maxSize && height < maxSize) {
                    finalWidth = width;
                    finalHeight = height;
                } else if (width > height) {
                    if ((float) width * 1.0F / (float) height <= (float) maxSize * 1.0F / (float) minSize) {
                        finalWidth = maxSize;
                        finalHeight = (int) ((float) maxSize * 1.0F / (float) width * (float) height);
                    } else {
                        finalWidth = maxSize;
                        finalHeight = minSize;
                    }
                } else if ((float) height * 1.0F / (float) width <= (float) maxSize * 1.0F / (float) minSize) {
                    finalHeight = maxSize;
                    finalWidth = (int) ((float) maxSize * 1.0F / (float) height * (float) width);
                } else {
                    finalHeight = maxSize;
                    finalWidth = minSize;
                }
            } else if (width < height) {
                finalWidth = minSize;
                finalHeight = Math.min((int) ((float) minSize * 1.0F / (float) width * (float) height), maxSize);
            } else {
                finalHeight = minSize;
                finalWidth = Math.min((int) ((float) minSize * 1.0F / (float) height * (float) width), maxSize);
            }

            ViewGroup.LayoutParams params = view.getLayoutParams();
            params.height = ScreenUtils.dip2px(view.getContext(), (float) (finalHeight / 2));
            params.width = ScreenUtils.dip2px(view.getContext(), (float) (finalWidth / 2));
            view.setLayoutParams(params);
        }
    }

    public Spannable getSummarySpannable(Context context, ImageMessage imageMessage) {
        return new SpannableString(context.getString(string.rc_conversation_summary_content_image));
    }
}

