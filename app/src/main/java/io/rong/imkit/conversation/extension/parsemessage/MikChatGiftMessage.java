package io.rong.imkit.conversation.extension.parsemessage;

import android.os.Parcel;
import android.text.TextUtils;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;

import io.rong.common.ParcelUtils;
import io.rong.imlib.MessageTag;
import io.rong.imlib.model.MessageContent;

@MessageTag(value = "VC:GiftMsg", flag = MessageTag.ISCOUNTED)
public class MikChatGiftMessage extends MessageContent {
    private static final String TAG = "MikChatMessage";
    // 自定义消息变量，可以有多个
    public String id;  //礼物ID
    public String showName; //礼物名称
    public String icon;//礼物图标
    public String call_id; //通话ID
    public String coin;//用户金币
    public String diamond;//主播钻石
    public String resource_id;//资源id
//    public String giftDirection;//0发送者,1接受者

    private MikChatGiftMessage() {}

    /**
     * 设置文字消息的内容。
     *{"type":"1","channelId":"1585bd70f4de488fac95741485803dce","userId":"1663916137398743042","userCategory":"B"}
     * @param content 文字消息的内容。
     */
//    public void setContent(String content) {
//        this.content = content;
//    }

    /**
     * 构造函数。
     *
     * @param in 初始化传入的 Parcel。
     */
    public MikChatGiftMessage(Parcel in) {
        setExtra(ParcelUtils.readFromParcel(in));
//        setContent(ParcelUtils.readFromParcel(in));
        id = ParcelUtils.readFromParcel(in);
        showName = ParcelUtils.readFromParcel(in);
        icon = ParcelUtils.readFromParcel(in);
        call_id = ParcelUtils.readFromParcel(in);
        coin = ParcelUtils.readFromParcel(in);
        diamond = ParcelUtils.readFromParcel(in);
        resource_id = ParcelUtils.readFromParcel(in);
    }

    // 快速构建消息对象方法
    public static MikChatGiftMessage obtain(String id, String showName, String icon, String call_id, String coin, String diamond, String resource_id) {
        MikChatGiftMessage msg = new MikChatGiftMessage();
        msg.id = id;
        msg.showName = showName;
        msg.icon = icon;
        msg.call_id = call_id;
        msg.coin = coin;
        msg.diamond = diamond;
        msg.resource_id = resource_id;
        return msg;
    }

    public static MikChatGiftMessage obtain(String id, String showName, String icon, String call_id, String resource_id) {
        MikChatGiftMessage msg = new MikChatGiftMessage();
        msg.id = id;
        msg.showName = showName;
        msg.icon = icon;
        msg.call_id = call_id;
        msg.resource_id = resource_id;
        return msg;
    }


    /** 创建 MikChatMessage(byte[] data) 带有 byte[] 的构造方法用于解析消息内容. */
    public MikChatGiftMessage(byte[] data) {
        if (data == null) {
            return;
        }
        String jsonStr = null;
        try {
            jsonStr = new String(data, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "UnsupportedEncodingException ", e);
        }
        if (jsonStr == null) {
            Log.e(TAG, "jsonStr is null ");
            return;
        }

        try {
            JSONObject jsonObj = new JSONObject(jsonStr);
            // 消息携带用户信息时, 自定义消息需添加下面代码
            if (jsonObj.has("user")) {
                setUserInfo(parseJsonToUserInfo(jsonObj.getJSONObject("user")));
            }
            // 用于群组聊天, 消息携带 @ 人信息时, 自定义消息需添加下面代码
            if (jsonObj.has("mentionedInfo")) {
                setMentionedInfo(parseJsonToMentionInfo(jsonObj.getJSONObject("mentionedInfo")));
            }
            // 将所有自定义变量从收到的 json 解析并赋值
            if (jsonObj.has("id")) {
                id = jsonObj.optString("id");
            }
            if (jsonObj.has("showName")) {
                showName = jsonObj.optString("showName");
            }
            if (jsonObj.has("icon")) {
                icon = jsonObj.optString("icon");
            }
            if (jsonObj.has("call_id")) {
                call_id = jsonObj.optString("call_id");
            }
            if (jsonObj.has("coin")) {
                coin = jsonObj.optString("coin");
            }

            if (jsonObj.has("diamond")) {
                diamond = jsonObj.optString("diamond");
            }
            if (jsonObj.has("resource_id")) {
                resource_id = jsonObj.optString("resource_id");
            }

            if (jsonObj.has("extra")) setExtra(jsonObj.optString("extra"));


        } catch (JSONException e) {
            Log.e(TAG, "JSONException " + e.getMessage());
        }
    }

    /**
     * 将本地消息对象序列化为消息数据。
     *
     * @return 消息数据。
     *
     * {"type":"1","channelId":"1585bd70f4de488fac95741485803dce","userId":"1663916137398743042","userCategory":"B"}
     */
    @Override
    public byte[] encode() {
        JSONObject jsonObj = new JSONObject();
        try {
            // 消息携带用户信息时, 自定义消息需添加下面代码
            if (getJSONUserInfo() != null) {
                jsonObj.putOpt("user", getJSONUserInfo());
            }
            // 用于群组聊天, 消息携带 @ 人信息时, 自定义消息需添加下面代码
            if (getJsonMentionInfo() != null) {
                jsonObj.putOpt("mentionedInfo", getJsonMentionInfo());
            }
            if (!TextUtils.isEmpty(this.getExtra())) {
                jsonObj.put("extra", this.getExtra());
            }
            //  将所有自定义消息的内容，都序列化至 json 对象中
            jsonObj.put("id", this.id);
            jsonObj.put("showName", this.showName);
            jsonObj.put("icon", this.icon);
            jsonObj.put("call_id", this.call_id);
            jsonObj.put("coin", this.coin);
            jsonObj.put("diamond", this.diamond);
            jsonObj.put("resource_id",      this.resource_id);
        } catch (JSONException e) {
            Log.e(TAG, "JSONException " + e.getMessage());
        }

        try {
            return jsonObj.toString().getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "UnsupportedEncodingException ", e);
        }
        return null;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int i) {
        // 对消息属性进行序列化，将类的数据写入外部提供的 Parcel 中
        ParcelUtils.writeToParcel(dest, getExtra());
        ParcelUtils.writeToParcel(dest, id);
        ParcelUtils.writeToParcel(dest, showName);
        ParcelUtils.writeToParcel(dest, icon);
        ParcelUtils.writeToParcel(dest, call_id);
        ParcelUtils.writeToParcel(dest, coin);
        ParcelUtils.writeToParcel(dest, diamond);
        ParcelUtils.writeToParcel(dest, resource_id);
    }

    public static final Creator<MikChatGiftMessage> CREATOR =
            new Creator<MikChatGiftMessage>() {
                public MikChatGiftMessage createFromParcel(Parcel source) {
                    return new MikChatGiftMessage(source);
                }

                public MikChatGiftMessage[] newArray(int size) {
                    return new MikChatGiftMessage[size];
                }
            };
}