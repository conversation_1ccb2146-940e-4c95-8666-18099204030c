package io.rong.imkit.conversation.extension.provider;

import android.content.Context;
import android.text.Spannable;
import android.text.SpannableString;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;


import com.mobile.app.facee.R;

import java.util.List;

import io.rong.imkit.conversation.extension.parsemessage.MikChatSystemMessage;
import io.rong.imkit.conversation.messgelist.provider.BaseNotificationMessageItemProvider;
import io.rong.imkit.model.UiMessage;
import io.rong.imkit.widget.adapter.IViewProviderListener;
import io.rong.imkit.widget.adapter.ViewHolder;
import io.rong.imlib.model.MessageContent;

public class SystemNoticeMessageItemProvider extends BaseNotificationMessageItemProvider<MikChatSystemMessage> {

    @Override
    protected ViewHolder onCreateMessageContentViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.rc_messag_system_item, parent, false);
        return new ViewHolder(parent.getContext(), view);
    }

    @Override
    protected void bindMessageContentViewHolder(final ViewHolder holder, ViewHolder parentHolder, MikChatSystemMessage message, final UiMessage uiMessage, int position, List<UiMessage> list, IViewProviderListener<UiMessage> listener) {

        final TextView noticeTitle = holder.getView(R.id.notice_title);
        final TextView noticeContent = holder.getView(R.id.notice_content);
        final TextView checkNow = holder.getView(R.id.tv_check_now);
        final View spaceLine = holder.getView(R.id.space_line);


//        noticeTitle.setText(message.title);
        noticeContent.setText(message.content);
        checkNow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                AppUtil.INSTANCE.jumpWithType(holder.getContext(), message.jump_url);
            }
        });

        if (message.jump_url.isEmpty()) {
            checkNow.setVisibility(View.GONE);
            spaceLine.setVisibility(View.GONE);
        } else {
            checkNow.setVisibility(View.VISIBLE);
            spaceLine.setVisibility(View.VISIBLE);
        }
    }


    @Override
    protected boolean isMessageViewType(MessageContent messageContent) {
        return messageContent instanceof MikChatSystemMessage;
    }


    @Override
    public Spannable getSummarySpannable(Context context, MikChatSystemMessage MikChatNoticeMessage) {
        return new SpannableString(context.getString(R.string.rc_system_notice));
    }

}
