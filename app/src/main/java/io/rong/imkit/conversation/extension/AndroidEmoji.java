//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package io.rong.imkit.conversation.extension;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ReplacementSpan;
import android.util.DisplayMetrics;

import androidx.annotation.NonNull;
import androidx.emoji2.text.EmojiCompat;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.rong.common.rlog.RLog;

public class AndroidEmoji {
    private static final String TAG = "AndroidEmoji";
    private static float density;
    private static Context mContext;
    private static final int MAX_DISPLAY_EMOJI = 600;
    private static Boolean useEmoji;
    private static Map<Integer, EmojiInfo> sEmojiMap;
    private static Map<Integer, String> replaceEmojiMap;
    private static List<EmojiInfo> sEmojiList;

    public static void init(Context context) {
        sEmojiMap = new HashMap();
        sEmojiList = new ArrayList();
        mContext = context.getApplicationContext();
        int[] codes = context.getResources().getIntArray(context.getResources().getIdentifier("rc_emoji_code", "array", context.getPackageName()));
        TypedArray array = context.getResources().obtainTypedArray(context.getResources().getIdentifier("rc_emoji_res", "array", context.getPackageName()));
        TypedArray strArray = context.getResources().obtainTypedArray(context.getResources().getIdentifier("rc_emoji_description", "array", context.getPackageName()));
        if (codes.length != array.length()) {
            throw new RuntimeException("Emoji resource init fail.");
        } else {
            int i = -1;

            while(true) {
                ++i;
                if (i >= codes.length) {
                    initReplaceEmojiMap();
                    DisplayMetrics dm = context.getResources().getDisplayMetrics();
                    density = dm.density;
                    array.recycle();
                    return;
                }

                EmojiInfo emoji = new EmojiInfo(codes[i], array.getResourceId(i, -1), strArray.getResourceId(i, -1));
                sEmojiMap.put(codes[i], emoji);
                sEmojiList.add(emoji);
            }
        }
    }

    private static void initReplaceEmojiMap() {
        if (replaceEmojiMap == null) {
            replaceEmojiMap = new HashMap();
        }

        replaceEmojiMap.put(9729, "☁️");
        replaceEmojiMap.put(9786, "☺️");
        replaceEmojiMap.put(10084, "❤️");
        replaceEmojiMap.put(9889, "⚡️");
        replaceEmojiMap.put(9728, "☀️");
        replaceEmojiMap.put(10052, "❄️");
        replaceEmojiMap.put(9748, "☔️");
        replaceEmojiMap.put(9996, "✌️");
        replaceEmojiMap.put(9757, "☝️");
        replaceEmojiMap.put(9749, "☕️");
        replaceEmojiMap.put(9999, "✏️");
    }

    public static List<EmojiInfo> getEmojiList() {
        return sEmojiList;
    }

    public static int getEmojiCount(String input) {
        if (input == null) {
            return 0;
        } else {
            int count = 0;
            char[] chars = input.toCharArray();

            for(int i = 0; i < chars.length; ++i) {
                if (!Character.isHighSurrogate(chars[i])) {
                    int codePoint;
                    if (Character.isLowSurrogate(chars[i])) {
                        if (i <= 0 || !Character.isSurrogatePair(chars[i - 1], chars[i])) {
                            continue;
                        }

                        codePoint = Character.toCodePoint(chars[i - 1], chars[i]);
                    } else {
                        codePoint = chars[i];
                    }

                    if (sEmojiMap != null && sEmojiMap.containsKey(codePoint)) {
                        ++count;
                    }
                }
            }

            return count;
        }
    }

    public static CharSequence ensure(String input) {
        CharSequence cs;
        if (useEmoji2()) {
            try {
                cs = EmojiCompat.get().process(input);
            } catch (Exception e) {
                RLog.i("AndroidEmoji", "ensure input:" + e.toString());
                cs = input;
            }
        } else {
            cs = input;
        }

        SpannableStringBuilder ssb = new SpannableStringBuilder(cs);
        int start = 0;
        int offset = start;
        int rcEmojiCode = 0;

        while(offset < cs.length()) {
            if (Character.isHighSurrogate(cs.charAt(offset))) {
                ++offset;
            } else {
                if (Character.isLowSurrogate(cs.charAt(offset))) {
                    if (offset > 0 && Character.isSurrogatePair(cs.charAt(offset - 1), cs.charAt(offset))) {
                        int codePoint = Character.toCodePoint(cs.charAt(offset - 1), cs.charAt(offset));
                        if (sEmojiMap != null && sEmojiMap.containsKey(codePoint) && needReplaceEmoji(cs, offset)) {
                            start = offset - 1;
                            ssb.setSpan(new EmojiImageSpan(codePoint), start, offset + 1, 33);
                        }
                    }
                } else {
                    int codePoint = cs.charAt(offset);
                    if (sEmojiMap != null && sEmojiMap.containsKey(codePoint) && needReplaceEmoji(cs, offset)) {
                        ssb.setSpan(new EmojiImageSpan(codePoint), offset, offset + 1, 33);
                    }
                }

                ++offset;
            }
        }

        return ssb;
    }

    private static boolean needReplaceEmoji(CharSequence cs, int offset) {
        if (offset >= cs.length() - 1) {
            return true;
        } else {
            return cs.charAt(offset + 1) != 8205;
        }
    }

    public static boolean isEmoji(String input) {
        if (input == null) {
            return false;
        } else {
            char[] chars = input.toCharArray();
            int length = chars.length;

            for(int i = 0; i < length; ++i) {
                if (!Character.isHighSurrogate(chars[i])) {
                    int codePoint;
                    if (Character.isLowSurrogate(chars[i])) {
                        if (i <= 0 || !Character.isSurrogatePair(chars[i - 1], chars[i])) {
                            continue;
                        }

                        codePoint = Character.toCodePoint(chars[i - 1], chars[i]);
                    } else {
                        codePoint = chars[i];
                    }

                    if (sEmojiMap != null && sEmojiMap.containsKey(codePoint)) {
                        return true;
                    }
                }
            }

            return false;
        }
    }

    public static void ensure(Spannable spannable, float textSize) {
        CharSequence cs;
        if (useEmoji2()) {
            try {
                cs = EmojiCompat.get().process(spannable);
            } catch (Exception e) {
                RLog.i("AndroidEmoji", "ensure spannable:" + e.toString());
                cs = spannable;
            }
        } else {
            cs = spannable;
        }

        int start = 0;
        int offset = start;
        int rcEmojiCode = 0;

        while(offset < cs.length()) {
            if (Character.isHighSurrogate(cs.charAt(offset))) {
                ++offset;
            } else {
                if (Character.isLowSurrogate(cs.charAt(offset))) {
                    if (offset > 0 && Character.isSurrogatePair(cs.charAt(offset - 1), cs.charAt(offset))) {
                        int codePoint = Character.toCodePoint(cs.charAt(offset - 1), cs.charAt(offset));
                        if (sEmojiMap != null && sEmojiMap.containsKey(codePoint) && needReplaceEmoji(cs, offset)) {
                            start = offset - 1;
                            spannable.setSpan(new EmojiImageSpan(codePoint), start, offset + 1, 33);
                        }
                    }
                } else {
                    int codePoint = cs.charAt(offset);
                    if (sEmojiMap != null && sEmojiMap.containsKey(codePoint) && needReplaceEmoji(cs, offset)) {
                        spannable.setSpan(new EmojiImageSpan(codePoint), offset, offset + 1, 33);
                    }
                }

                ++offset;
            }
        }

    }

    public static void ensure(Spannable spannable) {
        ensure(spannable, 0.0F);
    }

    public static SpannableStringBuilder replaceEmojiWithText(Spannable spannable) {
        if (spannable == null) {
            return null;
        } else {
            char[] chars = spannable.toString().toCharArray();
            String resultSpanStr = getReplaceEmojiText(chars, spannable.toString(), true);
            return new SpannableStringBuilder(resultSpanStr);
        }
    }

    public static String replaceEmojiWithText(String input, boolean replaceUnicodeEmoji) {
        if (input == null) {
            return null;
        } else {
            char[] chars = input.toCharArray();
            return getReplaceEmojiText(chars, input, replaceUnicodeEmoji);
        }
    }

    private static String getReplaceEmojiText(final char[] chars, String srcString, boolean replaceUnicodeEmoji) {
        int emojiCount = 0;
        StringBuilder resultSpanStr = new StringBuilder();

        for(int i = 0; i < chars.length; ++i) {
            if (!Character.isHighSurrogate(chars[i])) {
                int codePoint;
                boolean isSurrogatePair;
                if (Character.isLowSurrogate(chars[i])) {
                    if (i <= 0 || !Character.isSurrogatePair(chars[i - 1], chars[i])) {
                        continue;
                    }

                    codePoint = Character.toCodePoint(chars[i - 1], chars[i]);
                    isSurrogatePair = true;
                } else {
                    codePoint = chars[i];
                    isSurrogatePair = false;
                }

                if (mContext != null && sEmojiMap != null && sEmojiMap.containsKey(codePoint)) {
                    ++emojiCount;
                    char[] spanchars = srcString.toCharArray();
                    if (spanchars != null && spanchars.length > 0) {
                        if (emojiCount > 600) {
                            resultSpanStr.append("[");
                            EmojiInfo emojiInfo = (EmojiInfo)sEmojiMap.get(codePoint);
                            if (emojiInfo != null) {
                                resultSpanStr.append(mContext.getResources().getString(emojiInfo.strId));
                            }

                            resultSpanStr.append("]");
                        } else {
                            resultSpanStr = appendSpanStr(isSurrogatePair, resultSpanStr, chars, i, codePoint, replaceUnicodeEmoji);
                        }
                    } else {
                        resultSpanStr = appendSpanStr(isSurrogatePair, resultSpanStr, chars, i, codePoint, replaceUnicodeEmoji);
                    }
                } else {
                    resultSpanStr = appendSpanStr(isSurrogatePair, resultSpanStr, chars, i, codePoint, replaceUnicodeEmoji);
                }
            }
        }

        return resultSpanStr == null ? null : resultSpanStr.toString();
    }

    private static StringBuilder appendSpanStr(boolean isSurrogatePair, StringBuilder resultSpanStr, char[] chars, int index, int codePoint, boolean replaceUnicodeEmoji) {
        if (resultSpanStr == null) {
            return null;
        } else if (replaceUnicodeEmoji && replaceEmojiMap != null && replaceEmojiMap.containsKey(codePoint)) {
            resultSpanStr.append((String)replaceEmojiMap.get(codePoint));
            return resultSpanStr;
        } else {
            if (isSurrogatePair) {
                if (index - 1 >= 0) {
                    resultSpanStr.append(chars[index - 1]);
                    resultSpanStr.append(chars[index]);
                }
            } else if (index >= 0) {
                resultSpanStr.append(chars[index]);
            }

            return resultSpanStr;
        }
    }

    public static int getEmojiSize() {
        return sEmojiMap != null ? sEmojiMap.size() : 0;
    }

    public static int getEmojiCode(int index) {
        if (index >= 0 && sEmojiList != null && index < sEmojiList.size()) {
            EmojiInfo info = (EmojiInfo)sEmojiList.get(index);
            return info.code;
        } else {
            RLog.e("AndroidEmoji", "getEmojiCode sEmojiList IndexOutOfBounds");
            return 0;
        }
    }

    public static Drawable getEmojiDrawable(Context context, int index) {
        Drawable drawable = null;
        if (index >= 0 && sEmojiList != null && index < sEmojiList.size()) {
            EmojiInfo emoji = (EmojiInfo)sEmojiList.get(index);
            drawable = context.getResources().getDrawable(emoji.resId);
        }

        return drawable;
    }

    public static boolean useEmoji2() {
        if (useEmoji != null) {
            return useEmoji;
        } else {
            try {
                Class<?> aClass = Class.forName("androidx.emoji2.text.EmojiCompat");
                useEmoji = true;
            } catch (ClassNotFoundException var1) {
                useEmoji = false;
            }

            return useEmoji;
        }
    }

    public static class EmojiImageSpan extends ReplacementSpan {
        Drawable mDrawable;
        public static final int ALIGN_BOTTOM = 0;
        private WeakReference<Drawable> mDrawableRef;

        private EmojiImageSpan(int codePoint) {
            this(codePoint, 0.0F);
        }

        public EmojiImageSpan(int codePoint, float textSize) {
            if (AndroidEmoji.mContext != null && AndroidEmoji.sEmojiMap != null && AndroidEmoji.sEmojiMap.containsKey(codePoint)) {
                EmojiInfo emojiInfo = (EmojiInfo)AndroidEmoji.sEmojiMap.get(codePoint);
                if (emojiInfo == null) {
                    return;
                }

                try {
                    this.mDrawable = AndroidEmoji.mContext.getResources().getDrawable(emojiInfo.resId);
                } catch (Resources.NotFoundException e) {
                    RLog.i("AndroidEmoji", "EmojiImageSpan NotFoundException:" + e);
                    return;
                }

                int offset;
                if (textSize == 0.0F) {
                    offset = -((int)(4.0F * AndroidEmoji.density));
                } else {
                    offset = (int)(textSize - 21.0F * AndroidEmoji.density);
                }

                int width = this.mDrawable.getIntrinsicWidth() + offset;
                int height = this.mDrawable.getIntrinsicHeight() + offset;
                this.mDrawable.setBounds(0, 0, width > 0 ? width : 0, height > 0 ? height : 0);
            }

        }

        public Drawable getDrawable() {
            return this.mDrawable;
        }

        public int getSize(@NonNull Paint paint, CharSequence text, int start, int end, Paint.FontMetricsInt fm) {
            Drawable d = this.getCachedDrawable();
            Rect rect = d.getBounds();
            if (fm != null) {
                fm.ascent = -rect.bottom;
                fm.descent = 0;
                fm.top = fm.ascent;
                fm.bottom = 0;
            }

            return rect.right;
        }

        public void draw(Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom, @NonNull Paint paint) {
            Drawable b = this.getCachedDrawable();
            canvas.save();
            int transY = bottom - b.getBounds().bottom;
            transY = (int)((float)transY - AndroidEmoji.density);
            canvas.translate(x, (float)transY);
            b.draw(canvas);
            canvas.restore();
        }

        private Drawable getCachedDrawable() {
            WeakReference<Drawable> wr = this.mDrawableRef;
            Drawable d = null;
            if (wr != null) {
                d = (Drawable)wr.get();
            }

            if (d == null) {
                d = this.getDrawable();
                this.mDrawableRef = new WeakReference(d);
            }

            return d;
        }
    }

    private static class EmojiInfo {
        int code;
        int resId;
        int strId;

        public EmojiInfo(int code, int resId) {
            this.code = code;
            this.resId = resId;
        }

        public EmojiInfo(int code, int resId, int strId) {
            this.code = code;
            this.resId = resId;
            this.strId = strId;
        }
    }
}
