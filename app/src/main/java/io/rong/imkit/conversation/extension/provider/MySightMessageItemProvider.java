package io.rong.imkit.conversation.extension.provider;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.text.Spannable;
import android.text.SpannableString;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;

import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;

import java.io.File;
import java.util.List;

import io.rong.common.rlog.RLog;
import io.rong.imkit.R.dimen;
import io.rong.imkit.R.id;
import io.rong.imkit.R.layout;
import io.rong.imkit.R.string;
import io.rong.imkit.conversation.messgelist.provider.BaseMessageItemProvider;
import io.rong.imkit.feature.resend.ResendManager;
import io.rong.imkit.model.UiMessage;
import io.rong.imkit.picture.tools.ScreenUtils;
import io.rong.imkit.utils.RongOperationPermissionUtils;
import io.rong.imkit.widget.CircleProgressView;
import io.rong.imkit.widget.adapter.IViewProviderListener;
import io.rong.imkit.widget.adapter.ViewHolder;
import io.rong.imlib.model.Message.SentStatus;
import io.rong.imlib.model.MessageContent;
import io.rong.message.SightMessage;

public class MySightMessageItemProvider extends BaseMessageItemProvider<SightMessage> {
    private Integer minShortSideSize;

    public MySightMessageItemProvider() {
        this.mConfig.showReadState = true;
        this.mConfig.showContentBubble = false;
        this.mConfig.showProgress = false;
    }

    protected ViewHolder onCreateMessageContentViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(layout.rc_item_sight_message, parent, false);
        return new ViewHolder(view.getContext(), view);
    }

    protected void bindMessageContentViewHolder(ViewHolder holder, ViewHolder parentHolder, SightMessage sightMessage, UiMessage uiMessage, int position, List<UiMessage> list, IViewProviderListener<UiMessage> listener) {
        int progress = uiMessage.getProgress();
        SentStatus status = uiMessage.getMessage().getSentStatus();
        holder.setVisible(id.rc_sight_thumb, true);
        Uri thumbUri = sightMessage.getThumbUri();
        if (thumbUri != null && thumbUri.getPath() != null) {
            final ImageView imageView = (ImageView) holder.getView(id.rc_sight_thumb);
            final ImageView readyButton = (ImageView) holder.getView(id.rc_sight_tag);
            if (!this.checkViewsValid(new View[]{imageView})) {
                RLog.e("BaseMessageItemProvider", "checkViewsValid error," + uiMessage.getObjectName());
                return;
            }

            RoundedCorners roundedCorners = new RoundedCorners(ScreenUtils.dip2px(holder.getContext(), 6.0F));
            RequestOptions options = (RequestOptions) RequestOptions.bitmapTransform(roundedCorners).override(300, 300);
            Glide.with(imageView).load(new File(thumbUri.getPath())).apply(options).listener(new RequestListener<Drawable>() {
                public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                    return false;
                }

                public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                    measureLayoutParams(imageView, readyButton, resource);
                    return false;
                }
            }).into(imageView);
        }

        holder.setText(id.rc_sight_duration, this.getSightDuration(sightMessage.getDuration()));
        CircleProgressView loadingProgress = (CircleProgressView) holder.getView(id.rc_sight_progress);
        ProgressBar compressProgress = (ProgressBar) holder.getView(id.compressVideoBar);
        if (!this.checkViewsValid(new View[]{loadingProgress, compressProgress})) {
            RLog.e("BaseMessageItemProvider", "checkViewsValid error," + uiMessage.getObjectName());
        } else {
            if (progress > 0 && progress < 100) {
                loadingProgress.setProgress(progress, true);
                holder.setVisible(id.rc_sight_tag, false);
                loadingProgress.setVisibility(View.VISIBLE);
                compressProgress.setVisibility(View.GONE);
            } else if (status.equals(SentStatus.SENDING)) {
                holder.setVisible(id.rc_sight_tag, false);
                loadingProgress.setVisibility(View.GONE);
                compressProgress.setVisibility(View.VISIBLE);
            } else if (status.equals(SentStatus.FAILED) && ResendManager.getInstance().needResend(uiMessage.getMessage().getMessageId())) {
                holder.setVisible(id.rc_sight_tag, false);
                loadingProgress.setVisibility(View.GONE);
                compressProgress.setVisibility(View.VISIBLE);
            } else {
                holder.setVisible(id.rc_sight_tag, true);
                loadingProgress.setVisibility(View.GONE);
                compressProgress.setVisibility(View.GONE);
            }

        }
    }

    protected boolean onItemClick(ViewHolder holder, SightMessage sightMessage, UiMessage uiMessage, int position, List<UiMessage> list, IViewProviderListener<UiMessage> listener) {
        if (sightMessage != null) {
            if (!RongOperationPermissionUtils.isMediaOperationPermit(holder.getContext())) {
                return true;
            } else {
//                Intent intent = new Intent(holder.getContext(), VideoPlayActivity.class);
//                if (sightMessage.getMediaUrl() != null) {
//                    intent.putExtra("video_url", sightMessage.getMediaUrl().toString());
//                }
//                if (holder.getContext() instanceof MyRongConversationActivity rongConversationActivity) {
//                    intent.putExtra(Constants.INTENT_PARAM_KEY_ANCHOR_UID, rongConversationActivity.getTargetId());
//                }
//                intent.putExtra("sight_message_uid", uiMessage.getMessage().getUId());
//
//                if (uiMessage.getUserInfo() != null) {
//                    intent.putExtra("anchor_header", uiMessage.getUserInfo().getPortraitUri().toString());
//                }
//
//                holder.getContext().startActivity(intent);
                return true;
            }
        } else {
            return false;
        }
    }

    protected boolean isMessageViewType(MessageContent messageContent) {
        return messageContent instanceof SightMessage && !messageContent.isDestruct();
    }

    private void measureLayoutParams(View view, ImageView readyButton, Drawable drawable) {
        float width = (float) drawable.getIntrinsicWidth();
        float height = (float) drawable.getIntrinsicHeight();
        int minSize = 100;
        if (this.minShortSideSize == null) {
            this.minShortSideSize = ScreenUtils.dip2px(view.getContext(), 140.0F);
        }

        if (this.minShortSideSize > 0) {
            if (!(width >= (float) this.minShortSideSize) && !(height >= (float) this.minShortSideSize)) {
                ViewGroup.LayoutParams params = view.getLayoutParams();
                params.height = (int) height;
                params.width = (int) width;
                view.setLayoutParams(params);
                this.measureReadyButton(readyButton, drawable, width, height);
            } else {
                float scale = width / height;
                int finalWidth;
                int finalHeight;
                if (scale > 1.0F) {
                    finalHeight = (int) ((float) this.minShortSideSize / scale);
                    if (finalHeight < minSize) {
                        finalHeight = minSize;
                    }

                    finalWidth = this.minShortSideSize;
                } else {
                    finalHeight = this.minShortSideSize;
                    finalWidth = (int) ((float) this.minShortSideSize * scale);
                    if (finalWidth < minSize) {
                        finalWidth = minSize;
                    }
                }

                ViewGroup.LayoutParams params = view.getLayoutParams();
                params.height = finalHeight;
                params.width = finalWidth;
                view.setLayoutParams(params);
                this.measureReadyButton(readyButton, drawable, (float) finalWidth, (float) finalHeight);
            }
        }

    }

    private void measureReadyButton(ImageView readyButton, Drawable drawable, float finalWidth, float finalHeight) {
        if (readyButton != null && drawable != null) {
            int intrinsicHeight = drawable.getIntrinsicHeight();
            int intrinsicWidth = drawable.getIntrinsicWidth();
            if (intrinsicHeight != 0 && intrinsicWidth != 0 && finalHeight != 0.0F && finalWidth != 0.0F) {
                ViewGroup.LayoutParams layoutParams = readyButton.getLayoutParams();
                int readyButtonSize;
                if ((double) intrinsicWidth / ((double) finalWidth * (double) 1.0F) > (double) intrinsicHeight / ((double) finalHeight * (double) 1.0F)) {
                    readyButtonSize = (int) ((double) finalHeight * ((double) intrinsicHeight / ((double) intrinsicWidth * (double) 1.0F)));
                } else {
                    readyButtonSize = (int) ((double) finalWidth * ((double) intrinsicWidth / ((double) intrinsicHeight * (double) 1.0F)));
                }

                int min = Math.min(readyButtonSize, readyButton.getResources().getDimensionPixelSize(dimen.rc_sight_play_size));
                layoutParams.width = min;
                layoutParams.height = min;
                readyButton.setLayoutParams(layoutParams);
            }
        }
    }

    private String getSightDuration(int time) {
        if (time <= 0) {
            return "00:00";
        } else {
            int minute = time / 60;
            String recordTime;
            if (minute < 60) {
                int second = time % 60;
                recordTime = this.unitFormat(minute) + ":" + this.unitFormat(second);
            } else {
                int hour = minute / 60;
                if (hour > 99) {
                    return "99:59:59";
                }

                minute %= 60;
                int second = time - hour * 3600 - minute * 60;
                recordTime = this.unitFormat(hour) + ":" + this.unitFormat(minute) + ":" + this.unitFormat(second);
            }

            return recordTime;
        }
    }

    private String unitFormat(int time) {
        String formatTime;
        if (time >= 0 && time < 10) {
            formatTime = "0" + time;
        } else {
            formatTime = "" + time;
        }

        return formatTime;
    }

    public Spannable getSummarySpannable(Context context, SightMessage sightMessage) {
        return new SpannableString(context.getString(string.rc_conversation_summary_content_sight));
    }
}
