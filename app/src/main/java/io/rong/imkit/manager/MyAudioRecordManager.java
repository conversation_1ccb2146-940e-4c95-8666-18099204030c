package io.rong.imkit.manager;

import android.annotation.TargetApi;
import android.content.Context;
import android.media.AudioManager;
import android.media.MediaRecorder;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;
import android.telephony.PhoneStateListener;
import android.telephony.TelephonyManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;

import io.rong.common.rlog.RLog;
import io.rong.imkit.IMCenter;
import io.rong.imkit.R.drawable;
import io.rong.imkit.R.id;
import io.rong.imkit.R.layout;
import io.rong.imkit.R.string;
import io.rong.imkit.config.RongConfigCenter;
import io.rong.imkit.feature.destruct.DestructManager;
import io.rong.imlib.IRongCallback;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.common.SavePathUtils;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.ConversationIdentifier;
import io.rong.imlib.typingmessage.TypingMessageManager;
import io.rong.message.HQVoiceMessage;
import io.rong.message.VoiceMessage;

/**
 * Author:Lxf
 * Create on:2024/12/2
 * Description:
 */
public class MyAudioRecordManager implements Handler.Callback {
    private static final String TAG = "MyAudioRecordManager";
    private static final int RC_SAMPLE_RATE_8000 = 8000;
    private static final int RC_SAMPLE_RATE_16000 = 16000;
    private static final String VOICE_PATH = "/voice/";
    private final int AUDIO_RECORD_EVENT_TRIGGER;
    private final int AUDIO_RECORD_EVENT_SAMPLING;
    private final int AUDIO_RECORD_EVENT_WILL_CANCEL;
    private final int AUDIO_RECORD_EVENT_CONTINUE;
    private final int AUDIO_RECORD_EVENT_RELEASE;
    private final int AUDIO_RECORD_EVENT_ABORT;
    private final int AUDIO_RECORD_EVENT_TIME_OUT;
    private final int AUDIO_RECORD_EVENT_TICKER;
    private final int AUDIO_RECORD_EVENT_SEND_FILE;
    private final int AUDIO_AA_ENCODING_BIT_RATE;
    IAudioState idleState;
    IAudioState recordState;
    IAudioState sendingState;
    IAudioState cancelState;
    IAudioState timerState;
    private int RECORD_INTERVAL;
    private SamplingRate mSampleRate;
    private IAudioState mCurAudioState;
    private View mRootView;
    private Context mContext;
    private ConversationIdentifier mConversationIdentifier;
    private Handler mHandler;
    private AudioManager mAudioManager;
    private MediaRecorder mMediaRecorder;
    private Uri mAudioPath;
    private long smStartRecTime;
    private AudioManager.OnAudioFocusChangeListener mAfChangeListener;
    private PopupWindow mRecordWindow;
    private ImageView mStateIV;
    private TextView mStateTV;
    private TextView mTimerTV;

    @TargetApi(21)
    private MyAudioRecordManager() {
        this.AUDIO_RECORD_EVENT_TRIGGER = 1;
        this.AUDIO_RECORD_EVENT_SAMPLING = 2;
        this.AUDIO_RECORD_EVENT_WILL_CANCEL = 3;
        this.AUDIO_RECORD_EVENT_CONTINUE = 4;
        this.AUDIO_RECORD_EVENT_RELEASE = 5;
        this.AUDIO_RECORD_EVENT_ABORT = 6;
        this.AUDIO_RECORD_EVENT_TIME_OUT = 7;
        this.AUDIO_RECORD_EVENT_TICKER = 8;
        this.AUDIO_RECORD_EVENT_SEND_FILE = 9;
        this.AUDIO_AA_ENCODING_BIT_RATE = 32000;
        this.idleState = new IdleState();
        this.recordState = new RecordState();
        this.sendingState = new SendingState();
        this.cancelState = new CancelState();
        this.timerState = new TimerState();
        this.RECORD_INTERVAL = 60;
        this.mSampleRate = SamplingRate.RC_SAMPLE_RATE_8000;
        RLog.d("MyAudioRecordManager", "MyAudioRecordManager");
        this.mHandler = new Handler(Looper.getMainLooper(), this);
        this.mCurAudioState = this.idleState;
        this.idleState.enter();
    }

    public static MyAudioRecordManager getInstance() {
        return SingletonHolder.sInstance;
    }

    public final boolean handleMessage(Message msg) {
        RLog.i("MyAudioRecordManager", "handleMessage " + msg.what);
        AudioStateMessage m;
        switch (msg.what) {
            case 2:
                this.sendEmptyMessage(2);
                break;
            case 7:
                m = new AudioStateMessage();
                m.what = msg.what;
                m.obj = msg.obj;
                this.sendMessage(m);
                break;
            case 8:
                m = new AudioStateMessage();
                m.what = 7;
                m.obj = msg.obj;
                this.sendMessage(m);
        }

        return false;
    }

    private void initView(View root) {
        LayoutInflater inflater = LayoutInflater.from(root.getContext());
        View view = inflater.inflate(layout.rc_voice_record_popup, (ViewGroup) null);
        this.mStateIV = (ImageView) view.findViewById(id.rc_audio_state_image);
        this.mStateTV = (TextView) view.findViewById(id.rc_audio_state_text);
        this.mTimerTV = (TextView) view.findViewById(id.rc_audio_timer);
        this.mRecordWindow = new PopupWindow(view, -1, -1);
        this.mRecordWindow.showAtLocation(root, 17, 0, 0);
        this.mRecordWindow.setFocusable(true);
        this.mRecordWindow.setOutsideTouchable(false);
        this.mRecordWindow.setTouchable(false);
    }

    private void setTimeoutView(int counter) {
        if (counter > 0) {
            if (this.mRecordWindow != null) {
                this.mStateIV.setVisibility(View.GONE);
                this.mStateTV.setVisibility(View.VISIBLE);
                this.mStateTV.setText(string.rc_voice_rec);
                this.mStateTV.setBackgroundResource(android.R.color.transparent);
                this.mTimerTV.setText(String.format("%s", counter));
                this.mTimerTV.setVisibility(View.VISIBLE);
            }
        } else if (this.mRecordWindow != null) {
            this.mStateIV.setVisibility(View.VISIBLE);
            this.mStateIV.setImageResource(drawable.rc_voice_volume_warning);
            this.mStateTV.setText(string.rc_voice_too_long);
            this.mStateTV.setBackgroundResource(android.R.color.transparent);
            this.mTimerTV.setVisibility(View.GONE);
        }

    }

    private void setRecordingView() {
        RLog.d("MyAudioRecordManager", "setRecordingView");
        if (this.mRecordWindow != null) {
            this.mStateIV.setVisibility(View.VISIBLE);
            this.mStateIV.setImageResource(drawable.rc_voice_volume_1);
            this.mStateTV.setVisibility(View.VISIBLE);
            this.mStateTV.setText(string.rc_voice_rec);
            this.mStateTV.setBackgroundResource(android.R.color.transparent);
            this.mTimerTV.setVisibility(View.GONE);
        }

    }

    private void setCancelView() {
        RLog.d("MyAudioRecordManager", "setCancelView");
        if (this.mRecordWindow != null) {
            this.mTimerTV.setVisibility(View.GONE);
            this.mStateIV.setVisibility(View.VISIBLE);
            this.mStateIV.setImageResource(drawable.rc_voice_volume_cancel);
            this.mStateTV.setVisibility(View.VISIBLE);
            this.mStateTV.setText(string.rc_voice_cancel);
            this.mStateTV.setBackgroundResource(drawable.rc_voice_cancel_background);
        }

    }

    private void setCallStateChangeListener() {
        if (Build.VERSION.SDK_INT < 21) {
            try {
                if (this.mContext == null) {
                    return;
                }

                TelephonyManager manager = (TelephonyManager) this.mContext.getSystemService(Context.TELEPHONY_SERVICE);
                manager.listen(new PhoneStateListener() {
                    public void onCallStateChanged(int state, String incomingNumber) {
                        switch (state) {
                            case 1:
                                MyAudioRecordManager.this.sendEmptyMessage(6);
                            case 0:
                            case 2:
                            default:
                                super.onCallStateChanged(state, incomingNumber);
                        }
                    }
                }, 32);
            } catch (SecurityException var2) {
                SecurityException e = var2;
                RLog.e("MyAudioRecordManager", "MyAudioRecordManager", e);
            }
        }

    }

    private void destroyView() {
        RLog.d("MyAudioRecordManager", "destroyView");
        if (this.mRecordWindow != null) {
            this.mHandler.removeMessages(7);
            this.mHandler.removeMessages(8);
            this.mHandler.removeMessages(2);
            this.mRecordWindow.dismiss();
            this.mRecordWindow = null;
            this.mStateIV = null;
            this.mStateTV = null;
            this.mTimerTV = null;
            this.mContext = null;
            this.mRootView = null;
        }

    }

    public int getMaxVoiceDuration() {
        return this.RECORD_INTERVAL;
    }

    /**
     * @deprecated
     */
    @Deprecated
    public void setMaxVoiceDuration(int maxVoiceDuration) {
        if (maxVoiceDuration > 0) {
            this.RECORD_INTERVAL = maxVoiceDuration;
        }
    }

    public void startRecord(View rootView, ConversationIdentifier conversationIdentifier) {
        if (rootView != null) {
            this.mRootView = rootView;
            this.mContext = rootView.getContext().getApplicationContext();
            this.mConversationIdentifier = conversationIdentifier;
            this.mAudioManager = (AudioManager) this.mContext.getSystemService(Context.AUDIO_SERVICE);
            if (this.mAfChangeListener != null) {
                this.mAudioManager.abandonAudioFocus(this.mAfChangeListener);
                this.mAfChangeListener = null;
            }

            this.mAfChangeListener = new AudioManager.OnAudioFocusChangeListener() {
                public void onAudioFocusChange(int focusChange) {
                    RLog.d("MyAudioRecordManager", "OnAudioFocusChangeListener " + focusChange);
                    if (focusChange == -1) {
                        MyAudioRecordManager.this.mAudioManager.abandonAudioFocus(MyAudioRecordManager.this.mAfChangeListener);
                        MyAudioRecordManager.this.mAfChangeListener = null;
                        MyAudioRecordManager.this.mHandler.post(new Runnable() {
                            public void run() {
                                MyAudioRecordManager.this.sendEmptyMessage(6);
                            }
                        });
                    }

                }
            };
            this.sendEmptyMessage(1);
            if (TypingMessageManager.getInstance().isShowMessageTyping() && conversationIdentifier.getType().equals(Conversation.ConversationType.PRIVATE)) {
                RongIMClient.getInstance().sendTypingStatus(conversationIdentifier.getType(), this.mConversationIdentifier.getTargetId(), "RC:VcMsg");
            }

        }
    }

    public void willCancelRecord() {
        this.sendEmptyMessage(3);
    }

    public void continueRecord() {
        this.sendEmptyMessage(4);
    }

    public void stopRecord() {
        this.sendEmptyMessage(5);
    }

    public void destroyRecord() {
        AudioStateMessage msg = new AudioStateMessage();
        msg.obj = true;
        msg.what = 5;
        this.sendMessage(msg);
    }

    void sendMessage(AudioStateMessage message) {
        this.mCurAudioState.handleMessage(message);
    }

    void sendEmptyMessage(int event) {
        AudioStateMessage message = new AudioStateMessage();
        message.what = event;
        this.mCurAudioState.handleMessage(message);
    }

    private void startRec() {
        RLog.d("MyAudioRecordManager", "startRec");

        try {
            muteAudioFocus(mAudioManager, true);
            mAudioManager.setMode(AudioManager.MODE_NORMAL);
            mMediaRecorder = new MediaRecorder();
            mMediaRecorder.setOnErrorListener(new MediaRecorder.OnErrorListener() {
                public void onError(MediaRecorder mr, int what, int extra) {
                    RLog.e("MyAudioRecordManager", "MediaRecorder:onError: what = " + what + ", extra = " + extra);
                }
            });
            int bpsNb = RongConfigCenter.featureConfig().getAudioNBEncodingBitRate();
            int bpsWb = RongConfigCenter.featureConfig().getAudioWBEncodingBitRate();
            if (RongConfigCenter.featureConfig().getVoiceMessageType() == IMCenter.VoiceMessageType.HighQuality) {
                mMediaRecorder.setAudioEncodingBitRate(32000);
            } else {
                mMediaRecorder.setAudioSamplingRate(mSampleRate.value);
                if (mSampleRate.equals(SamplingRate.RC_SAMPLE_RATE_8000)) {
                    mMediaRecorder.setAudioEncodingBitRate(bpsNb);
                } else {
                    mMediaRecorder.setAudioEncodingBitRate(bpsWb);
                }
            }

            mMediaRecorder.setAudioChannels(1);
            mMediaRecorder.setAudioSource(MediaRecorder.AudioSource.MIC);
            if (RongConfigCenter.featureConfig().getVoiceMessageType().equals(IMCenter.VoiceMessageType.HighQuality)) {
                mMediaRecorder.setOutputFormat(MediaRecorder.OutputFormat.AAC_ADTS);
                mMediaRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.AAC);
            } else if (mSampleRate.equals(SamplingRate.RC_SAMPLE_RATE_8000)) {
                mMediaRecorder.setOutputFormat(MediaRecorder.OutputFormat.AMR_NB);
                mMediaRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.AMR_NB);
            } else {
                mMediaRecorder.setOutputFormat(MediaRecorder.OutputFormat.AMR_WB);
                mMediaRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.AMR_WB);
            }

            File savePath = SavePathUtils.getSavePath(mContext.getCacheDir());
            if (!savePath.exists()) {
                throw new FileNotFoundException(savePath.getPath());
            }

            if (!savePath.canWrite()) {
                boolean result = savePath.setWritable(true, true);
                if (!result) {
                    String msg = savePath.getPath() + " could not be writable.";
                    throw new IOException(msg);
                }
            }

            mAudioPath = Uri.fromFile(new File(savePath, System.currentTimeMillis() + "temp.voice"));
            mMediaRecorder.setOutputFile(mAudioPath.getPath());
            mMediaRecorder.prepare();
            mMediaRecorder.start();
            Message message = Message.obtain();
            message.what = 7;
            message.obj = 10;
            mHandler.removeMessages(7);
            mHandler.sendMessageDelayed(message, (long) (RECORD_INTERVAL * 1000 - 10000));
        } catch (RuntimeException | IOException var6) {
            Exception e = var6;
            RLog.e("MyAudioRecordManager", "startRec", e);
            if (mMediaRecorder != null) {
                mMediaRecorder.release();
                mMediaRecorder = null;
            }

            mHandler.sendEmptyMessage(6);
        }

    }

    private boolean checkAudioTimeLength() {
        long delta = SystemClock.elapsedRealtime() - smStartRecTime;
        return delta < 1000L;
    }

    private void stopRec() {
        RLog.d("MyAudioRecordManager", "stopRec");

        try {
            muteAudioFocus(mAudioManager, false);
            if (mMediaRecorder != null) {
                mMediaRecorder.stop();
                mMediaRecorder.release();
                mMediaRecorder = null;
            }
        } catch (Exception var2) {
            Exception e = var2;
            RLog.e("MyAudioRecordManager", "stopRec", e);
        }

    }

    private void deleteAudioFile() {
        RLog.d("MyAudioRecordManager", "deleteAudioFile");
        if (mAudioPath != null) {
            File file = new File(mAudioPath.getPath());
            if (file.exists()) {
                boolean deleteResult = file.delete();
                if (!deleteResult) {
                    RLog.e("MyAudioRecordManager", "deleteAudioFile delete file failed. path :" + mAudioPath.getPath());
                }
            }
        }

    }

    private void sendAudioFile() {
        RLog.d("MyAudioRecordManager", "sendAudioFile path = " + mAudioPath);
        if (mAudioPath != null) {
            File file = new File(mAudioPath.getPath());
            if (!file.exists() || file.length() == 0L) {
                RLog.e("MyAudioRecordManager", "sendAudioFile fail cause of file length 0 or audio permission denied");
                return;
            }

            int duration = (int) (SystemClock.elapsedRealtime() - smStartRecTime) / 1000;
            if (RongConfigCenter.featureConfig().getVoiceMessageType() == IMCenter.VoiceMessageType.HighQuality) {
                HQVoiceMessage hqVoiceMessage = HQVoiceMessage.obtain(mAudioPath, Math.min(duration, RECORD_INTERVAL));
                if (DestructManager.isActive()) {
                    hqVoiceMessage.setDestructTime((long) DestructManager.VOICE_DESTRUCT_TIME);
                }

                io.rong.imlib.model.Message message = io.rong.imlib.model.Message.obtain(mConversationIdentifier, hqVoiceMessage);
                IMCenter.getInstance().sendMediaMessage(message, DestructManager.isActive() ? mContext.getResources().getString(string.rc_conversation_summary_content_burn) : null, (String) null, new IRongCallback.ISendMediaMessageCallback() {
                    public void onProgress(io.rong.imlib.model.Message message, int progress) {
                    }

                    public void onCanceled(io.rong.imlib.model.Message message) {
                    }

                    public void onAttached(io.rong.imlib.model.Message message) {
                    }

                    public void onSuccess(io.rong.imlib.model.Message message) {
                    }

                    public void onError(io.rong.imlib.model.Message message, RongIMClient.ErrorCode errorCode) {
                        RLog.d("MyAudioRecordManager", "onError = " + errorCode.toString());
                    }
                });
            } else {
                VoiceMessage voiceMessage = VoiceMessage.obtain(mAudioPath, Math.min(duration, RECORD_INTERVAL));
                if (DestructManager.isActive()) {
                    voiceMessage.setDestructTime((long) DestructManager.VOICE_DESTRUCT_TIME);
                }
                IMCenter.getInstance().sendMessage(io.rong.imlib.model.Message.obtain(mConversationIdentifier, voiceMessage), DestructManager.isActive() ? mContext.getResources().getString(string.rc_conversation_summary_content_burn) : null, (String) null, new IRongCallback.ISendMessageCallback() {
                    public void onAttached(io.rong.imlib.model.Message message) {
                    }

                    public void onSuccess(io.rong.imlib.model.Message message) {
                    }

                    public void onError(io.rong.imlib.model.Message message, RongIMClient.ErrorCode errorCode) {
                    }
                });
            }
        }

    }

    private void audioDBChanged() {
        if (mMediaRecorder != null) {
            int db = 0;

            try {
                db = mMediaRecorder.getMaxAmplitude() / 600;
            } catch (IllegalStateException var3) {
                RLog.e("MyAudioRecordManager", "audioDBChanged IllegalStateException");
            }

            switch (db / 5) {
                case 0:
                    mStateIV.setImageResource(drawable.rc_voice_volume_1);
                    break;
                case 1:
                    mStateIV.setImageResource(drawable.rc_voice_volume_2);
                    break;
                case 2:
                    mStateIV.setImageResource(drawable.rc_voice_volume_3);
                    break;
                case 3:
                    mStateIV.setImageResource(drawable.rc_voice_volume_4);
                    break;
                case 4:
                    mStateIV.setImageResource(drawable.rc_voice_volume_5);
                    break;
                case 5:
                    mStateIV.setImageResource(drawable.rc_voice_volume_6);
                    break;
                default:
                    mStateIV.setImageResource(drawable.rc_voice_volume_6);
            }
        }

    }

    private void muteAudioFocus(AudioManager audioManager, boolean bMute) {
        if (Build.VERSION.SDK_INT < 8) {
            RLog.d("MyAudioRecordManager", "muteAudioFocus Android 2.1 and below can not stop music");
        } else if (audioManager == null) {
            RLog.e("MyAudioRecordManager", "audioManager is null");
        } else {
            if (bMute) {
                audioManager.requestAudioFocus(mAfChangeListener, 3, 2);
            } else {
                audioManager.abandonAudioFocus(mAfChangeListener);
                mAfChangeListener = null;
            }

        }
    }

    public int getSamplingRate() {
        return mSampleRate.getValue();
    }

    public void setSamplingRate(SamplingRate sampleRate) {
        mSampleRate = sampleRate;
    }

    class AudioStateMessage {
        public int what;
        public Object obj;

        AudioStateMessage() {
        }

        public AudioStateMessage obtain() {
            return MyAudioRecordManager.this.new AudioStateMessage();
        }
    }

    abstract class IAudioState {
        IAudioState() {
        }

        void enter() {
        }

        abstract void handleMessage(AudioStateMessage message);
    }

    class TimerState extends IAudioState {
        TimerState() {
            super();
        }

        void handleMessage(AudioStateMessage msg) {
            RLog.d("MyAudioRecordManager", this.getClass().getSimpleName() + " handleMessage : " + msg.what);
            switch (msg.what) {
                case 3:
                    MyAudioRecordManager.this.setCancelView();
                    MyAudioRecordManager.this.mCurAudioState = MyAudioRecordManager.this.cancelState;
                case 4:
                default:
                    break;
                case 5:
                    MyAudioRecordManager.this.mHandler.postDelayed(new Runnable() {
                        public void run() {
                            MyAudioRecordManager.this.stopRec();
                            MyAudioRecordManager.this.sendAudioFile();
                            MyAudioRecordManager.this.destroyView();
                        }
                    }, 500L);
                    MyAudioRecordManager.this.mCurAudioState = MyAudioRecordManager.this.idleState;
                    MyAudioRecordManager.this.idleState.enter();
                    break;
                case 6:
                    MyAudioRecordManager.this.stopRec();
                    MyAudioRecordManager.this.destroyView();
                    MyAudioRecordManager.this.deleteAudioFile();
                    MyAudioRecordManager.this.mCurAudioState = MyAudioRecordManager.this.idleState;
                    MyAudioRecordManager.this.idleState.enter();
                    break;
                case 7:
                    int counter = (Integer) msg.obj;
                    if (counter >= 0) {
                        Message message = Message.obtain();
                        message.what = 8;
                        message.obj = counter - 1;
                        MyAudioRecordManager.this.mHandler.sendMessageDelayed(message, 1000L);
                        MyAudioRecordManager.this.setTimeoutView(counter);
                    } else {
                        MyAudioRecordManager.this.mHandler.postDelayed(new Runnable() {
                            public void run() {
                                MyAudioRecordManager.this.stopRec();
                                MyAudioRecordManager.this.sendAudioFile();
                                MyAudioRecordManager.this.destroyView();
                            }
                        }, 500L);
                        MyAudioRecordManager.this.mCurAudioState = MyAudioRecordManager.this.idleState;
                    }
            }

        }
    }

    class CancelState extends IAudioState {
        CancelState() {
            super();
        }

        void handleMessage(AudioStateMessage msg) {
            RLog.d("MyAudioRecordManager", this.getClass().getSimpleName() + " handleMessage : " + msg.what);
            switch (msg.what) {
                case 1:
                case 2:
                case 3:
                default:
                    break;
                case 4:
                    MyAudioRecordManager.this.setRecordingView();
                    MyAudioRecordManager.this.mCurAudioState = MyAudioRecordManager.this.recordState;
                    MyAudioRecordManager.this.sendEmptyMessage(2);
                    break;
                case 5:
                case 6:
                    MyAudioRecordManager.this.stopRec();
                    MyAudioRecordManager.this.destroyView();
                    MyAudioRecordManager.this.deleteAudioFile();
                    MyAudioRecordManager.this.mCurAudioState = MyAudioRecordManager.this.idleState;
                    MyAudioRecordManager.this.idleState.enter();
                    break;
                case 7:
                    int counter = (Integer) msg.obj;
                    if (counter > 0) {
                        Message message = Message.obtain();
                        message.what = 8;
                        message.obj = counter - 1;
                        MyAudioRecordManager.this.mHandler.sendMessageDelayed(message, 1000L);
                    } else {
                        MyAudioRecordManager.this.mHandler.postDelayed(new Runnable() {
                            public void run() {
                                MyAudioRecordManager.this.stopRec();
                                MyAudioRecordManager.this.sendAudioFile();
                                MyAudioRecordManager.this.destroyView();
                            }
                        }, 500L);
                        MyAudioRecordManager.this.mCurAudioState = MyAudioRecordManager.this.idleState;
                        MyAudioRecordManager.this.idleState.enter();
                    }
            }

        }
    }

    class SendingState extends IAudioState {
        SendingState() {
            super();
        }

        void handleMessage(AudioStateMessage message) {
            RLog.d("MyAudioRecordManager", "SendingState handleMessage " + message.what);
            switch (message.what) {
                case 9:
                    MyAudioRecordManager.this.stopRec();
                    if ((Boolean) message.obj) {
                        MyAudioRecordManager.this.sendAudioFile();
                    }

                    MyAudioRecordManager.this.destroyView();
                    MyAudioRecordManager.this.mCurAudioState = MyAudioRecordManager.this.idleState;
                default:
            }
        }
    }

    class RecordState extends IAudioState {
        RecordState() {
            super();
        }

        void handleMessage(AudioStateMessage msg) {
            RLog.d("MyAudioRecordManager", this.getClass().getSimpleName() + " handleMessage : " + msg.what);
            switch (msg.what) {
                case 2:
                    MyAudioRecordManager.this.audioDBChanged();
                    MyAudioRecordManager.this.mHandler.sendEmptyMessageDelayed(2, 150L);
                    break;
                case 3:
                    MyAudioRecordManager.this.setCancelView();
                    MyAudioRecordManager.this.mCurAudioState = MyAudioRecordManager.this.cancelState;
                case 4:
                default:
                    break;
                case 5:
                    final boolean checked = MyAudioRecordManager.this.checkAudioTimeLength();
                    boolean activityFinished = false;
                    if (msg.obj != null) {
                        activityFinished = (Boolean) msg.obj;
                    }

                    if (checked && !activityFinished) {
                        MyAudioRecordManager.this.mStateIV.setImageResource(drawable.rc_voice_volume_warning);
                        MyAudioRecordManager.this.mStateTV.setText(string.rc_voice_short);
                        MyAudioRecordManager.this.mHandler.removeMessages(2);
                    }

                    if (!activityFinished && MyAudioRecordManager.this.mHandler != null) {
                        MyAudioRecordManager.this.mHandler.postDelayed(new Runnable() {
                            public void run() {
                                AudioStateMessage message = MyAudioRecordManager.this.new AudioStateMessage();
                                message.what = 9;
                                message.obj = !checked;
                                MyAudioRecordManager.this.sendMessage(message);
                            }
                        }, 500L);
                        MyAudioRecordManager.this.mCurAudioState = MyAudioRecordManager.this.sendingState;
                    } else {
                        MyAudioRecordManager.this.stopRec();
                        if (!checked && activityFinished) {
                            MyAudioRecordManager.this.sendAudioFile();
                        }

                        MyAudioRecordManager.this.destroyView();
                        MyAudioRecordManager.this.mCurAudioState = MyAudioRecordManager.this.idleState;
                    }
                    break;
                case 6:
                    MyAudioRecordManager.this.stopRec();
                    MyAudioRecordManager.this.destroyView();
                    MyAudioRecordManager.this.deleteAudioFile();
                    MyAudioRecordManager.this.mCurAudioState = MyAudioRecordManager.this.idleState;
                    MyAudioRecordManager.this.idleState.enter();
                    break;
                case 7:
                    int counter = (Integer) msg.obj;
                    MyAudioRecordManager.this.setTimeoutView(counter);
                    MyAudioRecordManager.this.mCurAudioState = MyAudioRecordManager.this.timerState;
                    if (counter >= 0) {
                        Message message = Message.obtain();
                        message.what = 8;
                        message.obj = counter - 1;
                        MyAudioRecordManager.this.mHandler.sendMessageDelayed(message, 1000L);
                    } else {
                        MyAudioRecordManager.this.mHandler.postDelayed(new Runnable() {
                            public void run() {
                                MyAudioRecordManager.this.stopRec();
                                MyAudioRecordManager.this.sendAudioFile();
                                MyAudioRecordManager.this.destroyView();
                            }
                        }, 500L);
                        MyAudioRecordManager.this.mCurAudioState = MyAudioRecordManager.this.idleState;
                    }
            }

        }
    }

    class IdleState extends IAudioState {
        public IdleState() {
            super();
            RLog.d("MyAudioRecordManager", "IdleState");
        }

        void enter() {
            super.enter();
            if (MyAudioRecordManager.this.mHandler != null) {
                MyAudioRecordManager.this.mHandler.removeMessages(7);
                MyAudioRecordManager.this.mHandler.removeMessages(8);
                MyAudioRecordManager.this.mHandler.removeMessages(2);
            }

        }

        void handleMessage(AudioStateMessage msg) {
            RLog.d("MyAudioRecordManager", "IdleState handleMessage : " + msg.what);
            switch (msg.what) {
                case 1:
                    MyAudioRecordManager.this.initView(MyAudioRecordManager.this.mRootView);
                    MyAudioRecordManager.this.setRecordingView();
                    MyAudioRecordManager.this.startRec();
                    MyAudioRecordManager.this.setCallStateChangeListener();
                    MyAudioRecordManager.this.smStartRecTime = SystemClock.elapsedRealtime();
                    MyAudioRecordManager.this.mCurAudioState = MyAudioRecordManager.this.recordState;
                    MyAudioRecordManager.this.sendEmptyMessage(2);
                default:
            }
        }
    }

    static class SingletonHolder {
        static MyAudioRecordManager sInstance = new MyAudioRecordManager();

        SingletonHolder() {
        }
    }

    public static enum SamplingRate {
        RC_SAMPLE_RATE_8000(8000),
        RC_SAMPLE_RATE_16000(16000);

        private int value;

        private SamplingRate(int sampleRate) {
            this.value = sampleRate;
        }

        public int getValue() {
            return this.value;
        }
    }
}

