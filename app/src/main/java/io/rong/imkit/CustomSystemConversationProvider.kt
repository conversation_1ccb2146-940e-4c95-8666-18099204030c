package io.rong.imkit

import androidx.core.content.ContextCompat
import io.rong.imkit.config.RongConfigCenter
import io.rong.imkit.conversationlist.model.BaseUiConversation
import io.rong.imkit.conversationlist.provider.BaseConversationProvider
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message.SentStatus
import io.rong.message.RecallNotificationMessage

/**
 * <AUTHOR>
 * @date 2025/6/25 10:38
 * @description
 */
class CustomSystemConversationProvider : BaseConversationProvider() {
    override fun isItemViewType(item: BaseUiConversation): Boolean {
        return Conversation.ConversationType.SYSTEM == item.mCore.conversationType
    }

    override fun bindViewHolder(
        holder: ViewHolder,
        uiConversation: BaseUiConversation,
        position: Int,
        list: List<BaseUiConversation?>?,
        listener: IViewProviderListener<BaseUiConversation?>?
    ) {
        super.bindViewHolder(holder, uiConversation, position, list, listener)
        if (RongConfigCenter.featureConfig()
                .isReadReceiptConversationType(Conversation.ConversationType.PRIVATE) && RongConfigCenter.conversationConfig()
                .isShowReadReceipt(Conversation.ConversationType.PRIVATE) && uiConversation.mCore.senderUserId == RongIMClient.getInstance().currentUserId && uiConversation.mCore.sentStatus.value == SentStatus.READ.value && uiConversation.mCore.latestMessage !is RecallNotificationMessage
        ) {
            holder.setVisible(R.id.rc_conversation_read_receipt, true)
        } else {
            holder.setVisible(R.id.rc_conversation_read_receipt, false)
        }
        holder.convertView.setBackgroundColor(ContextCompat.getColor(holder.context, R.color.picture_color_transparent))
        if (uiConversation.mCore.isTop) {
//            holder.getView<View>(com.mobile.app.facee.R.id.top_label_view).makeVisible()
        } else {
//            holder.getView<View>(com.mobile.app.facee.R.id.top_label_view).makeGone()
        }
        holder.setVisible(R.id.divider, false)
    }
}