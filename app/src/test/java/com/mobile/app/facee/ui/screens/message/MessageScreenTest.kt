package com.mobile.app.facee.ui.screens.message

import org.junit.Test
import org.junit.Assert.*

/**
 * MessageScreen的单元测试
 */
class MessageScreenTest {

    @Test
    fun testTabTypeEnum() {
        // 测试Tab类型枚举
        assertEquals("Message", MessageScreen.TabType.MESSAGE.title)
        assertEquals("Calls", MessageScreen.TabType.CALLS.title)
        assertEquals("Follow", MessageScreen.TabType.FOLLOW.title)
    }

    @Test
    fun testConversationDataModel() {
        // 测试会话数据模型
        val conversation = MessageScreen.Conversation(
            id = "test_id",
            userId = "user_123",
            userName = "Test User",
            userAvatar = "https://example.com/avatar.jpg",
            lastMessage = "Hello World",
            lastMessageTime = System.currentTimeMillis(),
            unreadCount = 5,
            isOnline = true
        )

        assertEquals("test_id", conversation.id)
        assertEquals("user_123", conversation.userId)
        assertEquals("Test User", conversation.userName)
        assertEquals(5, conversation.unreadCount)
        assertTrue(conversation.isOnline)
    }

    @Test
    fun testCallRecordDataModel() {
        // 测试通话记录数据模型
        val callRecord = MessageScreen.CallRecord(
            id = "call_123",
            userId = "user_456",
            userName = "Caller",
            userAvatar = "https://example.com/caller.jpg",
            callType = MessageScreen.CallType.VIDEO,
            callTime = System.currentTimeMillis(),
            duration = 180,
            isIncoming = true,
            isMissed = false
        )

        assertEquals("call_123", callRecord.id)
        assertEquals("user_456", callRecord.userId)
        assertEquals(MessageScreen.CallType.VIDEO, callRecord.callType)
        assertEquals(180L, callRecord.duration)
        assertTrue(callRecord.isIncoming)
        assertFalse(callRecord.isMissed)
    }

    @Test
    fun testFollowUserDataModel() {
        // 测试关注用户数据模型
        val followUser = MessageScreen.FollowUser(
            id = "follow_789",
            userId = "user_789",
            userName = "Followed User",
            userAvatar = "https://example.com/follow.jpg",
            bio = "This is a bio",
            isOnline = false,
            followTime = System.currentTimeMillis(),
            isFollowingBack = true
        )

        assertEquals("follow_789", followUser.id)
        assertEquals("user_789", followUser.userId)
        assertEquals("This is a bio", followUser.bio)
        assertFalse(followUser.isOnline)
        assertTrue(followUser.isFollowingBack)
    }

    @Test
    fun testWhoSeeMeUserDataModel() {
        // 测试谁看了我数据模型
        val whoSeeMeUser = MessageScreen.WhoSeeMeUser(
            id = "who_001",
            userId = "viewer_123",
            userName = "Viewer",
            userAvatar = "https://example.com/viewer.jpg",
            viewTime = System.currentTimeMillis()
        )

        assertEquals("who_001", whoSeeMeUser.id)
        assertEquals("viewer_123", whoSeeMeUser.userId)
        assertEquals("Viewer", whoSeeMeUser.userName)
    }

    @Test
    fun testStateDefaultValues() {
        // 测试State的默认值
        val eventSink: (MessageScreen.Event) -> Unit = {}
        val state = MessageScreen.State(eventSink = eventSink)

        assertFalse(state.isLoading)
        assertEquals(MessageScreen.TabType.MESSAGE, state.selectedTab)
        assertTrue(state.conversations.isEmpty())
        assertTrue(state.matches.isEmpty())
        assertTrue(state.calls.isEmpty())
        assertTrue(state.follows.isEmpty())
        assertTrue(state.whoSeeMe.isEmpty())
    }
}
