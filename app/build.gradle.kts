import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Properties

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
    alias(libs.plugins.google.gradle.ksp)
    alias(libs.plugins.kotlin.serialization)
    id("kotlin-parcelize")
}

val keystorePropertiesFile = rootProject.file("gradle.properties")
val keystoreProperties = Properties()
keystoreProperties.load(keystorePropertiesFile.inputStream())

android {
    namespace = "com.mobile.app.facee"
    compileSdk = 36

    defaultConfig {
        applicationId = "com.mobile.app.facee"
        minSdk = 24
        targetSdk = 36
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        ndk {
            abiFilters.addAll(listOf("arm64-v8a", "armeabi-v7a"))
        }
    }

    signingConfigs {
        create("release") {
            storeFile = file(keystoreProperties["storeFile"] as String)
            storePassword = keystoreProperties["storePassword"] as String
            keyAlias = keystoreProperties["keyAlias"] as String
            keyPassword = keystoreProperties["keyPassword"] as String
        }
    }
    buildTypes {
        getByName("release") {
            isMinifyEnabled = true
            signingConfig = signingConfigs.getByName("release")
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
            )
            buildConfigField("String", "HOST", "\"https://api.heart-merge.com\"")
            buildConfigField(
                "String", "SOCKET_HOST", "\"wss://api.heart-merge.com/api/v1/socket/ws\""
            )
            buildConfigField(
                "String",
                "BUILD_TIME",
                "\"${LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))}\""
            )
        }
        create("uat") {
            initWith(getByName("release")) // 复用 release 配置
            matchingFallbacks.add("release")
            versionNameSuffix = ".uat"
            buildConfigField("String", "HOST", "\"https://api.heart-merge.com\"")
            buildConfigField(
                "String", "SOCKET_HOST", "\"wss://api.heart-merge.com/api/v1/socket/ws\""
            )
        }
        getByName("debug") {
            isMinifyEnabled = false
            isDebuggable = true
            versionNameSuffix = ".test"
            signingConfig = signingConfigs.getByName("release")
//            buildConfigField("String", "HOST", "\"https://api.heart-merge.com\"")
//            buildConfigField(
//                "String", "SOCKET_HOST", "\"wss://api.heart-merge.com/api/v1/socket/ws\""
//            )
            buildConfigField("String", "HOST", "\"https://api.idim888.com\"")
            buildConfigField("String", "SOCKET_HOST", "\"wss://api.idim888.com/api/v1/socket/ws\"")
            buildConfigField(
                "String",
                "BUILD_TIME",
                "\"${LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))}\""
            )
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
            )
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }
//    kotlin {
//        jvmToolchain {
//            languageVersion.set(JavaLanguageVersion.of(17))
//        }
//        compilerOptions {
//            jvmTarget.set(JvmTarget.JVM_17)
//        }
//    }


    buildFeatures {
        compose = true
        viewBinding = true
        buildConfig = true
    }

    applicationVariants.all {
        outputs.all {
            (this as com.android.build.gradle.internal.api.BaseVariantOutputImpl).outputFile
            val dateFormat = DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
            val currentDate = LocalDateTime.now().format(dateFormat)
            val apkName =
                "facee-${buildType.name}-${defaultConfig.versionName}-${defaultConfig.versionCode}-${currentDate}.apk"
            outputFileName = apkName
        }
    }
}

dependencies {
    implementation(project(":netlibrary"))
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.navigation.compose)

    // Compose
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)
    implementation(libs.androidx.constraintlayout.compose)
    implementation(libs.androidx.foundation)
    implementation(libs.androidx.material) // pullRefresh
    implementation(libs.splashscreen)
    implementation(libs.reorderable)
    implementation(libs.camera.view)

    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.auth.ktx)
    implementation(libs.firebase.analytics.ktx)
    implementation(libs.play.services.auth)
    implementation(libs.firebase.crashlytics.ktx)

    // Image loading
    implementation(libs.coil.compose)

    // Circuit
    implementation(libs.circuit.foundation)
    implementation(libs.circuit.overlay)
    implementation(libs.circuit.navigation)

    // Network
    implementation(libs.retrofit)
    implementation(libs.okhttp)
    implementation(libs.kotlinx.serialization.json)
    implementation(libs.converter.gson)
    implementation(libs.retrofit.kotlinx.serialization)
    implementation(libs.okhttp.logging)
    implementation(libs.squareup.moshi)
    implementation(libs.squareup.moshiKt)
    ksp(libs.squareup.moshiCodegen)
    implementation(libs.retrofitMoshi)

    // Coroutines
    implementation(libs.kotlinx.coroutines.android)

    // DataStore
    implementation(libs.androidx.datastore.preferences)
    implementation(libs.androidx.datastore.core)
    implementation(libs.androidx.runtime)
    implementation(libs.gson)
    implementation(libs.logger)
    implementation(libs.rongYun)
    implementation(libs.androidx.preference)
    implementation(libs.lifecycleProcess)
    implementation(libs.rtcFull)

    implementation(libs.androidx.media3.transformer)
    implementation(libs.androidx.media3.common)

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)
}