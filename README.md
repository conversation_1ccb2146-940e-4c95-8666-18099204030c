# Facee - 社交匹配应用

基于 Circuit 框架构建的现代化社交匹配应用，采用 Jetpack Compose 和 Material Design 3。

## 项目架构

### Circuit 框架
本项目使用 Slack 开源的 Circuit 框架，这是一个基于 Compose Runtime 的状态管理和导航框架。

#### 核心概念
- **Screen**: 定义应用中的各个屏幕
- **Presenter**: 管理屏幕的状态和业务逻辑
- **UI**: 负责渲染界面
- **State**: 屏幕的状态数据
- **Event**: 用户交互事件

### 项目结构

```
app/src/main/java/com/mobile/app/facee/
├── MainActivity.kt                 # 应用入口
├── di/
│   └── CircuitConfig.kt           # Circuit 配置和依赖注入
├── ui/
│   ├── navigation/
│   │   └── RootNavigator.kt       # 根导航组件
│   ├── screens/
│   │   ├── AppScreen.kt           # 屏幕定义
│   │   ├── MainScreen.kt          # 主屏幕（底部导航容器）
│   │   ├── MainPresenter.kt       # 主屏幕 Presenter
│   │   ├── MainUi.kt              # 主屏幕 UI
│   │   ├── home/                  # 首页模块
│   │   │   ├── HomeScreen.kt      # 首页状态和事件定义
│   │   │   ├── HomePresenter.kt   # 首页业务逻辑
│   │   │   └── HomeUi.kt          # 首页界面
│   │   ├── match/                 # 匹配模块
│   │   │   ├── MatchScreen.kt
│   │   │   ├── MatchPresenter.kt
│   │   │   └── MatchUi.kt
│   │   ├── message/               # 消息模块
│   │   │   ├── MessageScreen.kt
│   │   │   ├── MessagePresenter.kt
│   │   │   └── MessageUi.kt
│   │   ├── chat/                  # 聊天模块
│   │   │   ├── ChatScreen.kt
│   │   │   ├── ChatPresenter.kt
│   │   │   └── ChatUi.kt
│   │   └── mine/                  # 个人中心模块
│   │       ├── ProfileScreen.kt
│   │       ├── ProfilePresenter.kt
│   │       └── ProfileUi.kt
│   └── theme/                     # 主题配置
│       ├── Color.kt
│       ├── Theme.kt
│       └── Type.kt
```

## 功能模块

### 1. 首页 (Home)
- 动态展示
- 推荐用户
- 点赞和评论功能
- 用户关注

### 2. 匹配 (Match)
- 滑动卡片匹配
- 左滑不喜欢，右滑喜欢
- 超级喜欢功能
- 匹配成功提示

### 3. 消息 (Message)
- 聊天列表
- 新匹配展示
- 未读消息提醒
- 在线状态显示

### 4. 聊天 (Chat)
- 实时聊天界面
- 消息气泡
- 输入状态提示
- 查看用户资料

### 5. 个人中心 (Profile)
- 个人资料展示
- 照片管理
- 兴趣标签
- 统计信息
- 设置入口

## 技术栈

### 核心框架
- **Circuit**: 状态管理和导航
- **Jetpack Compose**: 现代化 UI 框架
- **Material Design 3**: 设计系统

### 依赖库
- **Coil**: 图片加载
- **Retrofit**: 网络请求
- **Kotlinx Serialization**: JSON 序列化
- **OkHttp**: HTTP 客户端

### 开发工具
- **Kotlin**: 主要编程语言
- **Android Gradle Plugin**: 构建工具
- **KSP**: 注解处理

## 代码规范

### 1. 命名规范
- 文件名使用 PascalCase
- 类名使用 PascalCase
- 函数和变量使用 camelCase
- 常量使用 UPPER_SNAKE_CASE

### 2. 包结构
- 按功能模块组织包结构
- 每个屏幕包含 Screen、Presenter、UI 三个文件
- 共享组件放在对应的包中

### 3. Circuit 模式
- Screen 对象定义状态和事件
- Presenter 处理业务逻辑
- UI 组件只负责渲染
- 通过 eventSink 进行事件传递

### 4. Compose 最佳实践
- 使用 @Composable 函数构建 UI
- 状态提升到合适的层级
- 使用 remember 缓存计算结果
- 避免在 Composable 中进行副作用操作

## 构建和运行

1. 克隆项目
```bash
git clone <repository-url>
cd Facee
```

2. 打开 Android Studio
3. 同步 Gradle 依赖
4. 运行应用

## 后续开发计划

- [ ] 实现真实的网络 API 集成
- [ ] 添加用户认证系统
- [ ] 实现实时聊天功能
- [ ] 添加推送通知
- [ ] 优化性能和用户体验
- [ ] 添加单元测试和 UI 测试
- [ ] 实现深度链接
- [ ] 添加多语言支持

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。
