package com.facee.netlibrary

import com.facee.netlibrary.coroutines.Await
import com.facee.netlibrary.coroutines.AwaitImpl
import com.facee.netlibrary.interception.NetWorkFailedInterceptor
import retrofit2.Call
import java.lang.reflect.Type

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/17 3:48 下午
 * @description： 
 */
fun <T> Call<T>.toResponse() = toParser()

fun <T> Call<T>.toParser(): Await<T> = AwaitImpl(this)

fun isAwait(type: Type) : Boolean {
    return type == Await::class.java
}

var netWorkFailedInterceptor: NetWorkFailedInterceptor? = null