package com.facee.netlibrary.coroutines

import com.facee.netlibrary.utils.await
import retrofit2.Call

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/17 3:48 下午
 * @description：
 */
internal class AwaitImpl<T>(
    private val call : Call<T>,
) : Await<T> {

    override suspend fun await(): T {

        return try {
            call.await()
        } catch (t: Throwable) {
            throw t
        }
    }
}