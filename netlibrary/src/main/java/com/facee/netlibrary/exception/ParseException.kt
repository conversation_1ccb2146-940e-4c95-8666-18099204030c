package com.facee.netlibrary.exception

import java.io.IOException

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/17 3:46 下午
 * @description： 
 */
class ParseException(code: Int, message: String?) :
    IOException(message) {
    val errorCode: Int = code
    override fun getLocalizedMessage(): String? {
        return message
    }

    override fun toString(): String {
        return """${javaClass.name}:
            Code=$errorCode message=$message"""
    }
}
